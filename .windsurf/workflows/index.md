---
description: T<PERSON><PERSON> liệu này đóng vai trò là chỉ mục trung tâm cho tất cả các quy tắc
---

# Inbox Zero AI - Master Rule Index

Tài liệu này đóng vai trò là chỉ mục trung tâm cho tất cả các quy tắc Windsurf đang hoạt động trong kho lưu trữ này, đ<PERSON><PERSON><PERSON> sắp xếp theo danh mục. Các quy tắc này xác định các quy trình, cấu trúc và hướng dẫn để phát triển.

## Core & General

Cấu trúc dự án cơ bản, thiết lập và hướng dẫn phát triển chung.

| Tệp quy tắc               | Mô tả                                                                   |
| :------------------------ | :---------------------------------------------------------------------- |
| @windsurf-rules.md        | Cách thêm hoặc chỉnh sửa các quy tắc Windsurf trong dự án của chúng tôi |
| @project-structure.md     | Hướng dẫn về cấu trúc dự án và tổ chức tệp                              |
| @installing-packages.md   | Cách cài đặt các gói                                                    |
| @environment-variables.md | Thêm biến môi trường                                                    |
| @utilities.md             | Các hàm tiện ích                                                        |
| @task-list.md             | Hướng dẫn tạo và quản lý danh sách tác vụ                               |

## Giao diện người dùng & Giao diện người dùng

Hướng dẫn xây dựng giao diện người dùng và xử lý logic giao diện người dùng.

| Tệp quy tắc          | Mô tả                                                                        |
| :------------------- | :--------------------------------------------------------------------------- |
| @page-structure.md   | Quy tắc tạo page mới với tách biệt logic và giao diện                        |
| @ui-components.md    | Hướng dẫn về thành phần và kiểu dáng giao diện người dùng (Shadcn, Tailwind) |
| @form-handling.md    | Xử lý biểu mẫu bằng React Hook Form và Zod                                   |
| @hooks.md            | Hướng dẫn tạo móc React tùy chỉnh                                            |
| @state-management.md | Quy tắc quản lý state và tách biệt logic                                     |

## Workflows

Quy trình làm việc chi tiết cho các tác vụ phổ biến.

| Workflow            | Mô tả                                                                       |
| :------------------ | :-------------------------------------------------------------------------- |
| @create-new-page.md | Workflow chi tiết để tạo page mới theo quy tắc tách biệt logic và giao diện |
