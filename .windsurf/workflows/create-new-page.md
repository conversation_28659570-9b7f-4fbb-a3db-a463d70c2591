---
trigger: user_request
description: Workflow để tạo page mới theo quy tắc tách biệt logic và giao diện
---

# Workflow: Tạo Page Mới

## Bước 1: <PERSON>ân tích yêu cầu

Trước khi tạo page, hãy xác định:
- [ ] Tên page và URL path
- [ ] <PERSON><PERSON><PERSON> chức năng ch<PERSON>h (CRUD, search, filter, etc.)
- [ ] Loại dữ liệu cần hiển thị (list, form, detail, etc.)
- [ ] Các component UI cần thiết
- [ ] State management requirements

## Bước 2: Tạo cấu trúc thư mục

```bash
mkdir -p src/app/[lang]/(dashboard-layout)/PAGE_NAME/{_components,_hooks,_lib,_types}
```

## Bước 3: Tạo Types và Interfaces

Tạo `_types/index.ts`:
```typescript
export interface PageData {
  id: string;
  name: string;
  // Thêm các fields khác
  createdAt: Date;
  updatedAt: Date;
}

export interface PageFilters {
  search?: string;
  status?: string;
  // Thêm các filters khác
}

export interface PageState {
  data: PageData[];
  loading: boolean;
  error: string | null;
  filters: PageFilters;
}
```

## Bước 4: Tạo API và Validation

Tạo `_lib/api.ts`:
```typescript
import { PageData, PageFilters } from '../_types';

export async function fetchPageData(filters?: PageFilters): Promise<PageData[]> {
  // Implementation
}

export async function createPageItem(data: Omit<PageData, 'id' | 'createdAt' | 'updatedAt'>): Promise<PageData> {
  // Implementation
}

export async function updatePageItem(id: string, data: Partial<PageData>): Promise<PageData> {
  // Implementation
}

export async function deletePageItem(id: string): Promise<void> {
  // Implementation
}
```

Tạo `_lib/validations.ts`:
```typescript
import { z } from 'zod';

export const pageDataSchema = z.object({
  name: z.string().min(1, 'Tên không được để trống'),
  // Thêm các validation khác
});

export type PageDataInput = z.infer<typeof pageDataSchema>;
```

## Bước 5: Tạo Custom Hooks

Tạo `_hooks/use-page-data.ts`:
```typescript
'use client';

import { useState, useEffect, useCallback } from 'react';
import { fetchPageData, createPageItem, updatePageItem, deletePageItem } from '../_lib/api';
import type { PageData, PageFilters } from '../_types';

export function usePageData() {
  const [data, setData] = useState<PageData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<PageFilters>({});

  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await fetchPageData(filters);
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const addItem = useCallback(async (item: Omit<PageData, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const newItem = await createPageItem(item);
      setData(prev => [...prev, newItem]);
      return newItem;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create item');
      throw err;
    }
  }, []);

  const updateItem = useCallback(async (id: string, updates: Partial<PageData>) => {
    try {
      const updatedItem = await updatePageItem(id, updates);
      setData(prev => prev.map(item => 
        item.id === id ? updatedItem : item
      ));
      return updatedItem;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update item');
      throw err;
    }
  }, []);

  const removeItem = useCallback(async (id: string) => {
    try {
      await deletePageItem(id);
      setData(prev => prev.filter(item => item.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete item');
      throw err;
    }
  }, []);

  return {
    data,
    loading,
    error,
    filters,
    setFilters,
    loadData,
    addItem,
    updateItem,
    removeItem,
  };
}
```

## Bước 6: Tạo Components

### 6.1 Page Header Component

Tạo `_components/page-header.tsx`:
```typescript
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface PageHeaderProps {
  title: string;
  description?: string;
  onCreateClick?: () => void;
}

export function PageHeader({ title, description, onCreateClick }: PageHeaderProps) {
  return (
    <div className="flex justify-between items-start">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
        {description && (
          <p className="text-muted-foreground mt-2">{description}</p>
        )}
      </div>
      {onCreateClick && (
        <Button onClick={onCreateClick}>
          <Plus className="w-4 h-4 mr-2" />
          Thêm mới
        </Button>
      )}
    </div>
  );
}
```

### 6.2 Data Table Component

Tạo `_components/data-table.tsx`:
```typescript
'use client';

import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Edit, Trash2 } from 'lucide-react';
import type { PageData } from '../_types';

interface DataTableProps {
  data: PageData[];
  onEdit?: (item: PageData) => void;
  onDelete?: (id: string) => void;
}

export function DataTable({ data, onEdit, onDelete }: DataTableProps) {
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Tên</TableHead>
            <TableHead>Ngày tạo</TableHead>
            <TableHead className="text-right">Thao tác</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.length === 0 ? (
            <TableRow>
              <TableCell colSpan={3} className="text-center py-8 text-muted-foreground">
                Không có dữ liệu
              </TableCell>
            </TableRow>
          ) : (
            data.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.name}</TableCell>
                <TableCell>{new Date(item.createdAt).toLocaleDateString('vi-VN')}</TableCell>
                <TableCell className="text-right space-x-2">
                  {onEdit && (
                    <Button variant="outline" size="sm" onClick={() => onEdit(item)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                  )}
                  {onDelete && (
                    <Button variant="outline" size="sm" onClick={() => onDelete(item.id)}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
```

## Bước 7: Tạo Main Page Content

Tạo `_components/page-content.tsx`:
```typescript
'use client';

import { useState } from 'react';
import { usePageData } from '../_hooks';
import { PageHeader } from './page-header';
import { DataTable } from './data-table';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import type { PageData } from '../_types';

export function PageContent() {
  const { data, loading, error, addItem, updateItem, removeItem } = usePageData();
  const [editingItem, setEditingItem] = useState<PageData | null>(null);

  const handleCreate = () => {
    // Open create dialog
  };

  const handleEdit = (item: PageData) => {
    setEditingItem(item);
    // Open edit dialog
  };

  const handleDelete = async (id: string) => {
    if (confirm('Bạn có chắc chắn muốn xóa?')) {
      await removeItem(id);
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Tên Page"
        description="Mô tả page"
        onCreateClick={handleCreate}
      />
      
      <DataTable
        data={data}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />
    </div>
  );
}
```

## Bước 8: Tạo Entry Point

Tạo `page.tsx`:
```typescript
import { Suspense } from 'react';
import { PageContent } from './_components/page-content';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

export default function PageName() {
  return (
    <div className="container mx-auto py-6">
      <Suspense fallback={<LoadingSpinner />}>
        <PageContent />
      </Suspense>
    </div>
  );
}
```

## Bước 9: Export Components

Tạo `_components/index.ts`:
```typescript
export { PageHeader } from './page-header';
export { PageContent } from './page-content';
export { DataTable } from './data-table';
```

Tạo `_hooks/index.ts`:
```typescript
export { usePageData } from './use-page-data';
```

## Checklist hoàn thành

- [ ] Tạo cấu trúc thư mục đúng quy tắc
- [ ] Định nghĩa types và interfaces
- [ ] Tạo API functions và validation schemas
- [ ] Implement custom hooks cho data management
- [ ] Tạo reusable components
- [ ] Sử dụng shadcn-ui components
- [ ] Tách biệt logic và giao diện
- [ ] Implement loading và error states
- [ ] Test functionality
- [ ] Optimize performance nếu cần
