---
trigger: model_decision
description: <PERSON><PERSON><PERSON> thêm hoặc chỉnh sửa quy tắc Windsurf trong dự án
---

# Vị trí Quy tắc Windsurf

Cách thêm quy tắc mới vào dự án

1. Luôn đặt file quy tắc trong PROJECT_ROOT/.cursor/rules/:

   ```
   .windsurf/rules/
   ├── your-rule-name.md
   ├── another-rule.md
   └── ...
   ```

2. Quy ước đặt tên:

   - Sử dụng kebab-case cho tên file
   - Luôn sử dụng đuôi .md
   - Đặt tên mô tả rõ mục đích của quy tắc

3. <PERSON><PERSON><PERSON> trúc thư mục:

   ```
   PROJECT_ROOT/
   ├── .windsurf/
   │   └── rules/
   │       ├── your-rule-name.md
   │       └── ...
   └── ...
   ```

4. Không bao giờ đặt file quy tắc:

   - Ở thư mục gốc dự án
   - Ở các thư mục con bên ngoài .windsurf/rules
   - Ở b<PERSON><PERSON> kỳ vị trí nào khác

5. <PERSON><PERSON><PERSON> quy tắc của Cursor có cấu trúc sau:

````
---
description: <PERSON><PERSON> tả ngắn gọn mục đích của quy tắc
globs: duong-dan-tuy-chon/**/*
alwaysApply: false

#Tiêu đề Quy tắc
Nội dung chính giải thích quy tắc với định dạng markdown.

1. Hướng dẫn từng bước
2. Ví dụ mã
3. Hướng dẫn
Ví dụ:

Example:
```typescript
// Ví dụ tốt
function goodExample() {
  // Triển khai tuân thủ hướng dẫn
}

// Ví dụ không tốt
function badExample() {
  // Triển khai không tuân thủ hướng dẫn
}
```
````
