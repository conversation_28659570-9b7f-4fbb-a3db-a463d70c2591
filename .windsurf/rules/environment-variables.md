---
trigger: model_decision
description: Thê<PERSON> biến môi trường mới
---

# <PERSON><PERSON> định về Biến Môi trường

Đ<PERSON>y là cách thêm biến môi trường mới vào dự án:

## 1. Thêm vào file `.env.example`

```env
# Biến phía server
DATABASE_URL="postgresql://user:password@localhost:5432/db_name"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# Biến phía client (phải có tiền tố NEXT_PUBLIC_)
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_API_URL="http://localhost:3000/api"
```

## 2. Cập nhật validation trong `src/env.js`

```typescript
// Đ<PERSON><PERSON> với biến server-side
server: {
  DATABASE_URL: z.string().url(),
  NEXTAUTH_SECRET: z.string().min(32),
  NEXTAUTH_URL: z.string().url().optional(),
},

// Đối với biến client-side
client: {
  NEXT_PUBLIC_APP_URL: z.string().url(),
  NEXT_PUBLIC_API_URL: z.string().url(),
},

// Runtime environment variables
runtimeEnv: {
  DATABASE_URL: process.env.DATABASE_URL,
  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
  NEXTAUTH_URL: process.env.NEXTAUTH_URL,
  NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
},
```

## 3. Sử dụng biến môi trường

### Trong Server Components/API Routes:

```typescript
import { env } from "@/env.js";

// Sử dụng biến server-side
const dbUrl = env.DATABASE_URL;
```

### Trong Client Components:

```typescript
"use client";
import { env } from "@/env.js";

// Sử dụng biến client-side
const apiUrl = env.NEXT_PUBLIC_API_URL;
```

## Quy tắc đặt tên biến

1. **Biến server-side**: Viết hoa, phân cách bằng dấu gạch dưới `_`

   - Ví dụ: `DATABASE_URL`, `NEXTAUTH_SECRET`

2. **Biến client-side**: Bắt buộc có tiền tố `NEXT_PUBLIC_`

   - Ví dụ: `NEXT_PUBLIC_API_URL`, `NEXT_PUBLIC_GA_MEASUREMENT_ID`

3. **Giá trị nhạy cảm**: Không bao giờ commit vào git, thêm vào `.gitignore`
   - Ví dụ: mật khẩu, API keys, secret keys

## Ví dụ cụ thể

### Thêm biến server-side mới:

1. Thêm vào `.env.example`:

   ```
   REDIS_URL="redis://localhost:6379"
   ```

2. Cập nhật `env.js`:
   ```typescript
   server: {
     // ... các biến khác
     REDIS_URL: z.string().url(),
   },
   runtimeEnv: {
     // ... các biến khác
     REDIS_URL: process.env.REDIS_URL,
   },
   ```

### Thêm biến client-side mới:

1. Thêm vào `.env.example`:

   ```
   NEXT_PUBLIC_GOOGLE_ANALYTICS_ID="G-XXXXXXXXXX"
   ```

2. Cập nhật `env.js`:
   ```typescript
   client: {
     // ... các biến khác
     NEXT_PUBLIC_GOOGLE_ANALYTICS_ID: z.string(),
   },
   runtimeEnv: {
     // ... các biến khác
     NEXT_PUBLIC_GOOGLE_ANALYTICS_ID: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,
   },
   ```

## Lưu ý quan trọng

1. Luôn cập nhật cả `.env.example` khi thêm biến mới
2. Không commit file `.env` vào git
3. Đảm bảo kiểm tra kiểu dữ liệu chính xác với Zod
4. Đặt giá trị mặc định phù hợp khi cần thiết
5. Đối với dự án production, cấu hình biến môi trường trên hosting service (Vercel, Netlify, etc.)

## Tham khảo

- `src/env.js`
- `.env.example`
- [Next.js Environment Variables Documentation](https://nextjs.org/docs/app/building-your-application/configuring/environment-variables)
