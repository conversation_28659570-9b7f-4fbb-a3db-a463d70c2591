---
trigger: model_decision
description: <PERSON>uy tắc quản lý state và tách biệt logic
---

# Quy tắc State Management và Logic Separation

## Nguyên tắc cơ bản

### 1. Tách biệt Logic khỏi UI Components
- **KHÔNG** viết business logic trực tiếp trong component
- Sử dụng custom hooks để encapsulate logic
- Component chỉ chứa JSX và state management cơ bản
- Tách biệt data fetching, validation, và side effects

### 2. Quản lý State theo cấp độ
- **Local State**: `useState`, `useReducer` cho component-specific state
- **Shared State**: Custom hooks với context cho state chia sẻ
- **Server State**: React Query hoặc SWR cho data từ API
- **Global State**: Zustand hoặc Redux cho app-wide state

### 3. Không Hard Code dữ liệu
- **LUÔN** sử dụng state để quản lý danh sách
- Implement CRUD operations đầy đủ
- Sử dụng proper loading và error states
- Cache data khi có thể

## Patterns cho Custom Hooks

### 1. Data Management Hook
```typescript
// _hooks/use-data-manager.ts
'use client';

import { useState, useEffect, useCallback } from 'react';

interface UseDataManagerOptions<T> {
  fetchFn: () => Promise<T[]>;
  createFn?: (item: Omit<T, 'id'>) => Promise<T>;
  updateFn?: (id: string, updates: Partial<T>) => Promise<T>;
  deleteFn?: (id: string) => Promise<void>;
}

export function useDataManager<T extends { id: string }>({
  fetchFn,
  createFn,
  updateFn,
  deleteFn,
}: UseDataManagerOptions<T>) {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await fetchFn();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [fetchFn]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const addItem = useCallback(async (item: Omit<T, 'id'>) => {
    if (!createFn) throw new Error('Create function not provided');
    
    try {
      const newItem = await createFn(item);
      setData(prev => [...prev, newItem]);
      return newItem;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create item');
      throw err;
    }
  }, [createFn]);

  const updateItem = useCallback(async (id: string, updates: Partial<T>) => {
    if (!updateFn) throw new Error('Update function not provided');
    
    try {
      const updatedItem = await updateFn(id, updates);
      setData(prev => prev.map(item => 
        item.id === id ? updatedItem : item
      ));
      return updatedItem;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update item');
      throw err;
    }
  }, [updateFn]);

  const removeItem = useCallback(async (id: string) => {
    if (!deleteFn) throw new Error('Delete function not provided');
    
    try {
      await deleteFn(id);
      setData(prev => prev.filter(item => item.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete item');
      throw err;
    }
  }, [deleteFn]);

  return {
    data,
    loading,
    error,
    loadData,
    addItem,
    updateItem,
    removeItem,
  };
}
```

### 2. Form State Hook
```typescript
// _hooks/use-form-state.ts
'use client';

import { useState, useCallback } from 'react';
import { z } from 'zod';

interface UseFormStateOptions<T> {
  schema: z.ZodSchema<T>;
  initialValues: T;
  onSubmit: (values: T) => Promise<void> | void;
}

export function useFormState<T>({
  schema,
  initialValues,
  onSubmit,
}: UseFormStateOptions<T>) {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const setValue = useCallback((field: keyof T, value: T[keyof T]) => {
    setValues(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  }, [errors]);

  const validate = useCallback(() => {
    try {
      schema.parse(values);
      setErrors({});
      return true;
    } catch (err) {
      if (err instanceof z.ZodError) {
        const newErrors: Partial<Record<keyof T, string>> = {};
        err.errors.forEach((error) => {
          const field = error.path[0] as keyof T;
          newErrors[field] = error.message;
        });
        setErrors(newErrors);
      }
      return false;
    }
  }, [values, schema]);

  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    e?.preventDefault();
    
    if (!validate()) return;

    try {
      setIsSubmitting(true);
      await onSubmit(values);
    } catch (err) {
      console.error('Form submission error:', err);
    } finally {
      setIsSubmitting(false);
    }
  }, [values, validate, onSubmit]);

  const reset = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setIsSubmitting(false);
  }, [initialValues]);

  return {
    values,
    errors,
    isSubmitting,
    setValue,
    validate,
    handleSubmit,
    reset,
  };
}
```

### 3. Dialog/Modal State Hook
```typescript
// _hooks/use-dialog-state.ts
'use client';

import { useState, useCallback } from 'react';

export function useDialogState<T = any>() {
  const [isOpen, setIsOpen] = useState(false);
  const [data, setData] = useState<T | null>(null);

  const openDialog = useCallback((dialogData?: T) => {
    setData(dialogData || null);
    setIsOpen(true);
  }, []);

  const closeDialog = useCallback(() => {
    setIsOpen(false);
    setData(null);
  }, []);

  return {
    isOpen,
    data,
    openDialog,
    closeDialog,
  };
}
```

## Component Patterns

### 1. Container/Presenter Pattern
```typescript
// Container Component (Logic)
function UserListContainer() {
  const { data, loading, error, addItem, updateItem, removeItem } = useDataManager({
    fetchFn: fetchUsers,
    createFn: createUser,
    updateFn: updateUser,
    deleteFn: deleteUser,
  });

  const { isOpen, data: editingUser, openDialog, closeDialog } = useDialogState<User>();

  return (
    <UserListPresenter
      users={data}
      loading={loading}
      error={error}
      onEdit={openDialog}
      onDelete={removeItem}
      onAdd={() => openDialog()}
    />
  );
}

// Presenter Component (UI)
interface UserListPresenterProps {
  users: User[];
  loading: boolean;
  error: string | null;
  onEdit: (user: User) => void;
  onDelete: (id: string) => void;
  onAdd: () => void;
}

function UserListPresenter({
  users,
  loading,
  error,
  onEdit,
  onDelete,
  onAdd,
}: UserListPresenterProps) {
  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;

  return (
    <div className="space-y-4">
      <div className="flex justify-between">
        <h2>Users</h2>
        <Button onClick={onAdd}>Add User</Button>
      </div>
      <UserTable users={users} onEdit={onEdit} onDelete={onDelete} />
    </div>
  );
}
```

### 2. Compound Component Pattern
```typescript
// _components/data-list.tsx
interface DataListProps {
  children: React.ReactNode;
  className?: string;
}

function DataList({ children, className }: DataListProps) {
  return <div className={cn("space-y-4", className)}>{children}</div>;
}

function DataListHeader({ children }: { children: React.ReactNode }) {
  return <div className="flex justify-between items-center">{children}</div>;
}

function DataListContent({ children }: { children: React.ReactNode }) {
  return <div className="space-y-2">{children}</div>;
}

function DataListEmpty({ message = "Không có dữ liệu" }: { message?: string }) {
  return (
    <div className="text-center py-8 text-muted-foreground">
      {message}
    </div>
  );
}

// Export compound component
DataList.Header = DataListHeader;
DataList.Content = DataListContent;
DataList.Empty = DataListEmpty;

export { DataList };

// Usage
function UserList() {
  const { data, loading } = useDataManager({ fetchFn: fetchUsers });

  return (
    <DataList>
      <DataList.Header>
        <h2>Users</h2>
        <Button>Add User</Button>
      </DataList.Header>
      
      <DataList.Content>
        {data.length === 0 ? (
          <DataList.Empty />
        ) : (
          data.map(user => <UserCard key={user.id} user={user} />)
        )}
      </DataList.Content>
    </DataList>
  );
}
```

## Best Practices

### 1. State Normalization
```typescript
// ❌ Nested state - khó update
interface AppState {
  users: {
    id: string;
    name: string;
    posts: {
      id: string;
      title: string;
      comments: Comment[];
    }[];
  }[];
}

// ✅ Normalized state - dễ update
interface AppState {
  users: Record<string, User>;
  posts: Record<string, Post>;
  comments: Record<string, Comment>;
}
```

### 2. Optimistic Updates
```typescript
const updateItem = useCallback(async (id: string, updates: Partial<T>) => {
  // Optimistic update
  setData(prev => prev.map(item => 
    item.id === id ? { ...item, ...updates } : item
  ));

  try {
    const updatedItem = await updateFn(id, updates);
    // Confirm with server response
    setData(prev => prev.map(item => 
      item.id === id ? updatedItem : item
    ));
  } catch (err) {
    // Revert on error
    loadData();
    throw err;
  }
}, [updateFn, loadData]);
```

### 3. Error Boundaries
```typescript
// _components/error-boundary.tsx
'use client';

import { Component, ReactNode } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <Alert variant="destructive">
          <AlertDescription>
            Đã xảy ra lỗi: {this.state.error?.message}
          </AlertDescription>
        </Alert>
      );
    }

    return this.props.children;
  }
}
```

## Checklist

- [ ] Logic tách biệt khỏi UI components
- [ ] Sử dụng custom hooks cho business logic
- [ ] State management phù hợp với scope
- [ ] Implement proper error handling
- [ ] Loading states cho UX tốt
- [ ] Optimistic updates khi phù hợp
- [ ] Error boundaries cho error handling
- [ ] Performance optimization (memo, callback)
- [ ] Type safety với TypeScript
- [ ] Testable code structure
