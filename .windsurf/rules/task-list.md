---
trigger: manual
---

# Qu<PERSON><PERSON> lý danh sách tác vụ

Hướng dẫn tạo và quản lý danh sách tác vụ trong tệp markdown để theo dõi tiến độ dự án

## Tạo danh sách tác vụ

1. Tạ<PERSON> danh sách tác vụ trong tệp markdown (trong thư mục gốc của dự án):

- Sử dụng `TASKS.md` hoặc tên mô tả có liên quan đến tính năng (ví dụ: `ASSISTANT_CHAT.md`)
- Bao gồm tiêu đề rõ ràng và mô tả về tính năng đang được triển khai

2. Cấu trúc tệp với các phần sau:

```markdown
# Tên tính năng Triển khai

Mô tả ngắn gọn về tính năng và mục đích của tính năng đó.

## Nhiệm vụ đã hoàn thành

- [x] Nhiệm vụ 1 đã hoàn thành
- [x] Nhiệm vụ 2 đã hoàn thành

## Nhiệm vụ đang tiến hành

- [ ] Nhiệm vụ 3 hiện đang được thực hiện
- [ ] Nhiệm vụ 4 sẽ sớm hoàn thành

## Nhiệm vụ trong tương lai

- [ ] Nhiệm vụ 5 được lên kế hoạch triển khai trong tương lai
- [ ] Nhiệm vụ 6 được lên kế hoạch triển khai trong tương lai

## Kế hoạch triển khai

Mô tả chi tiết về cách tính năng sẽ được triển khai.

### Tệp liên quan

- path/to/file1.ts - Mô tả mục đích
- path/to/file2.ts - Mô tả mục đích
```

## Bảo trì danh sách tác vụ

1. Cập nhật danh sách tác vụ khi bạn tiến hành:

- Đánh dấu các tác vụ là đã hoàn thành bằng cách thay đổi `[ ]` thành `[x]`
- Thêm các tác vụ mới khi chúng được xác định
- Di chuyển các tác vụ giữa các phần khi thích hợp

2. Giữ phần "Tệp liên quan" được cập nhật bằng:

- Đường dẫn tệp đã được tạo hoặc sửa đổi
- Mô tả ngắn gọn về mục đích của từng tệp
- Chỉ báo trạng thái (ví dụ: ✅) cho các thành phần đã hoàn thành

3. Thêm thông tin chi tiết về triển khai:

- Quyết định về kiến ​​trúc
- Mô tả luồng dữ liệu
- Các thành phần kỹ thuật cần thiết
- Cấu hình môi trường

## Hướng dẫn AI

Khi làm việc với danh sách tác vụ, AI nên:

1. Cập nhật thường xuyên tệp danh sách tác vụ sau khi triển khai các thành phần quan trọng
2. Đánh dấu các tác vụ đã hoàn thành bằng [x] khi hoàn thành
3. Thêm các tác vụ mới được phát hiện trong quá trình triển khai
4. Duy trì Phần "Tệp liên quan" có đường dẫn tệp và mô tả chính xác
5. Ghi lại chi tiết triển khai, đặc biệt là đối với các tính năng phức tạp
6. Khi triển khai từng tác vụ một, trước tiên hãy kiểm tra xem tác vụ nào sẽ triển khai tiếp theo
7. Sau khi triển khai một tác vụ, hãy cập nhật tệp để phản ánh tiến trình

## Ví dụ về Cập nhật tác vụ

Khi cập nhật tác vụ từ "Đang tiến hành" thành "Đã hoàn thành":

```markdown
## Nhiệm vụ đang tiến hành

- [ ] Triển khai lược đồ cơ sở dữ liệu
- [ ] Tạo điểm cuối API để truy cập dữ liệu

## Nhiệm vụ đã hoàn thành

- [x] Thiết lập cấu trúc dự án
- [x] Cấu hình biến môi trường
```

Nên trở thành:

```markdown
## Nhiệm vụ đang tiến hành

- [ ] Tạo điểm cuối API để truy cập dữ liệu

## Nhiệm vụ đã hoàn thành

- [x] Thiết lập cấu trúc dự án
- [x] Cấu hình biến môi trường
- [x] Triển khai lược đồ cơ sở dữ liệu
```
