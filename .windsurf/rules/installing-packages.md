---
trigger: model_decision
description: <PERSON><PERSON>ch cài đặt package
---

### Đối với các package thông thường

Sử dụng `bun` để cài đặt các package thông thường:

```bash
bun add <tên-package>
```

<PERSON><PERSON> dụ:

```bash
bun add react-hook-form
```

Đối với các package phát triển (devDependencies), thêm cờ `-D`:

```bash
bun add -D <tên-package>
```

### Đối với shadcn/ui

Khi làm việc với các component từ shadcn/ui, sử dụng `pnpm` để đảm bảo tương thích:

```bash
pnpm add <tên-package-shadcn>
```

Ví dụ khi thêm component từ shadcn:

```bash
pnpm dlx shadcn-ui@latest add button
```

### Cài đặt dependencies

Đ<PERSON> cài đặt tất cả các dependencies đã khai báo trong `package.json`:

```bash
bun install
```

hoặc nếu dùng pnpm:

```bash
pnpm install
```
