---
trigger: model_decision
description: <PERSON><PERSON><PERSON>ng dẫn sử dụng React hooks
---

# Quy ước sử dụng Hooks

Tài liệu này mô tả các quy ước và hướng dẫn sử dụng React hooks trong dự án.

## <PERSON><PERSON><PERSON> hooks hiện có

Dự án hiện đang sử dụng các hooks chính sau trong thư mục `src/hooks/`:

- `use-is-rtl.tsx`: Hook xử lý hướng giao diện RTL (Right-to-Left)
- `use-is-vertical.tsx`: Hook kiểm tra bố cục dọc
- `use-mobile.tsx`: Hook phát hiện thiết bị di động
- `use-mode.tsx`: Hook quản lý chế độ hiển thị (sáng/tối)
- `use-radius.tsx`: Hook quản lý bán kính bo tròn
- `use-settings.tsx`: Hook quản lý cài đặt ứng dụng
- `use-toast.tsx`: Hook hiển thị thông báo toast

## Quy ước tạo mới hooks

1. **Đặt tên:**

   - <PERSON><PERSON><PERSON> đầu bằng tiền tố `use`
   - Sử dụng camelCase
   - Rõ ràng, mô tả đúng chức năng
   - Ví dụ: `useDarkMode`, `useWindowSize`

2. **Vị trí đặt file:**

   - Hooks dùng chung: `src/hooks/`
   - Hooks riêng cho page: `app/[lang]/(route)/page-name/hooks/`
   - Hooks cho feature cụ thể: `app/[lang]/(route)/_features/feature-name/hooks/`

3. **Nguyên tắc:**

   - Mỗi hook chỉ nên thực hiện một nhiệm vụ cụ thể
   - Tái sử dụng code khi có thể
   - Xử lý cleanup các side effects trong `useEffect`
   - Đặt tên biến và hàm rõ ràng, dễ hiểu
   - Sử dụng TypeScript để định nghĩa rõ ràng các kiểu dữ liệu

4. **Tài liệu:**
   - Thêm comment mô tả chức năng hook
   - Ghi rõ các tham số đầu vào và giá trị trả về
   - Đưa ra ví dụ sử dụng nếu cần thiết

## Best Practices

- Sử dụng `useCallback` cho các hàm callback
- Sử dụng `useMemo` cho các giá trị tính toán phức tạp
- Tránh tạo quá nhiều state trong một hook
- Xử lý lỗi đầy đủ
- Kiểm tra component đã được mount trước khi cập nhật state

## Ví dụ mẫu

```typescript
/**
 * Hook tùy chỉnh để quản lý dark mode
 * @returns {object} Đối tượng chứa trạng thái dark mode và hàm toggle
 */
const useDarkMode = (initialValue = false) => {
  const [isDark, setIsDark] = useState(initialValue);

  const toggle = useCallback(() => {
    setIsDark(prev => !prev);
  }, []);

  return { isDark, toggle };
};
```
