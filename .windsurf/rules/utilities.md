---
trigger: model_decision
description: <PERSON><PERSON><PERSON> hàm tiện ích và công cụ hỗ trợ cho các tác vụ thông dụng
---

# C<PERSON><PERSON> hàm tiện ích

## Tiện ích cốt lõi

### `cn(...inputs: ClassValue[]): string`

Kết hợp các tên lớp CSS với hỗ trợ merge của Tailwind.

```tsx
import { cn } from "~/lib/utils";

// Sử dụng với các lớp điều kiện
const className = cn(
  "base-class",
  isActive && "active",
  isDisabled ? "opacity-50" : "hover:opacity-100"
);
```

### `getInitials(fullName: string): string`

Trích xuất các chữ cái đầu từ họ tên đầy đủ.

```ts
const initials = getInitials("Nguyễn Văn A"); // "NVA"
```

## Tiện ích chuỗi

### `isUrl(text: string): boolean`

<PERSON><PERSON><PERSON> tra xem một chuỗi có phải là URL hợp lệ không.

### `slugify(text: string): string`

Chuyển đổi chuỗi thành dạng URL thân thiện.

### Định dạng chuỗi

- `camelCaseToTitleCase(camelCaseStr: string): string` - Chuyển từ camelCase sang Title Case
- `titleCaseToCamelCase(titleCaseStr: string): string` - Chuyển từ Title Case sang camelCase
- `ensureWithPrefix(value: string, prefix: string): string` - Đảm bảo chuỗi có tiền tố
- `ensureWithSuffix(value: string, suffix: string): string` - Đảm bảo chuỗi có hậu tố
- `ensureWithoutPrefix(value: string, prefix: string): string` - Đảm bảo chuỗi không có tiền tố
- `ensureWithoutSuffix(value: string, suffix: string): string` - Đảm bảo chuỗi không có hậu tố

## Tiện ích số

### `isEven(num: number): boolean`

Kiểm tra một số có phải là số chẵn không.

### `isNonNegative(num: number): boolean`

Kiểm tra một số có không âm không.

### Các hàm định dạng

- `formatNumberToCompact(value: number, locales: LocaleType = "vi"): string` - Định dạng số gọn (ví dụ: 1K, 1.2M)
- `formatCurrency(value: number, locales: LocaleType = "vi", currency: string = "VND"): string` - Định dạng tiền tệ
- `formatPercent(value: number, locales: LocaleType = "vi"): string` - Định dạng phần trăm
- `ratingToPercentage(rating: number, maxRating: number, fractionDigits: number = 0): string` - Chuyển đổi đánh giá thành phần trăm

## Tiện ích ngày giờ

### Định dạng ngày tháng

- `formatDate(value: string | number | Date): string` - Định dạng "Ngày Tháng Năm"
- `formatDateWithTime(value: string | number | Date): string` - Định dạng ngày giờ đầy đủ
- `formatDateShort(value: string | number | Date): string` - Định dạng ngắn "Thg Ngày"
- `formatTime(value: string | number | Date): string` - Định dạng giờ "HH:MM"
- `formatRelativeDate(value?: string | number | Date): string` - Trả về "Hôm nay", "Hôm qua" hoặc ngày đã định dạng
- `formatDistance(value: string | number | Date): string` - Thời gian tương đối (ví dụ: "2 ngày trước")
- `formatDuration(value: string | number | Date): string` - Định dạng khoảng thời gian (ví dụ: "2h 30p")

### Hỗ trợ ngày tháng

- `isBeforeToday(date: Date): boolean` - Kiểm tra xem ngày có trước hôm nay không
- `timeToDate(timeString: string, baseDate = new Date()): Date` - Chuyển đổi chuỗi giờ thành đối tượng Date

## Tiện ích tập tin

### `formatFileSize(bytes: number, decimals: number = 2): string`

Định dạng kích thước tập tin dễ đọc (ví dụ: "2,5 MB").

### `formatFileType(type: string): string`

Trích xuất loại tập tin từ MIME type.

## Hỗ trợ đường dẫn và URL

### `isActivePathname(basePathname: string, currentPathname: string, exactMatch: boolean = false): boolean`

Kiểm tra xem một đường dẫn có đang hoạt động không, hỗ trợ so khớp một phần.

### `ensureRedirectPathname(basePathname: string, redirectPathname: string): string`

Đảm bảo định dạng đường dẫn chuyển hướng chính xác.

## Hỗ trợ giao diện

### `formatUnreadCount(unreadCount: number): string`

Định dạng số thông báo chưa đọc với "99+" cho số lượng lớn.

### `formatOverviewCardValue(value: number, formatStyle: FormatStyleType): string | number`

Định dạng giá trị cho thẻ tổng quan dựa trên kiểu.

## Tiện ích bất đồng bộ

### `wait(ms: number = 250): Promise<void>`

Hàm trễ đơn giản cho các mẫu async/await.

## Từ điển và đa ngôn ngữ

### `getDictionaryValue(key: string, section: Record<string, unknown>): string`

Lấy giá trị từ điển một cách an toàn với giá trị dự phòng.

## Thanh toán và giá cả

### `getDiscountedPrice(price: number, discountRate: number, isAnnual: boolean = false): number`

Tính giá sau khi giảm với tùy chọn giảm giá hàng năm.

### `getCreditCardBrandName(number: string): string`

Nhận diện thương hiệu thẻ tín dụng từ số thẻ.

## Thực hành tốt nhất

1. **Khả năng tái sử dụng**: Đặt các hàm tiện ích có thể tái sử dụng trong `src/lib/utils.ts`
2. **An toàn kiểu dữ liệu**: Luôn cung cấp kiểu TypeScript cho tham số và giá trị trả về
3. **Tài liệu hóa**: Thêm chú thích JSDoc cho các hàm phức tạp
4. **Kiểm thử**: Bao gồm các bài kiểm thử đơn vị trong thư mục `__tests__`
5. **Hiệu suất**: Sử dụng memoization cho các phép tính tốn tài nguyên
6. **Xử lý lỗi**: Kiểm tra đầu vào và cung cấp thông báo lỗi rõ ràng

## Các thư viện bên ngoài

- `date-fns` để thao tác ngày tháng
- `zod` để kiểm tra cấu trúc dữ liệu
- `clsx` và `tailwind-merge` để quản lý tên lớp
- `lodash` (chỉ import các hàm cần thiết)

## Tổ chức mã nguồn

- Giữ các hàm tiện ích đơn giản và không có tác dụng phụ khi có thể
- Nhóm các hàm liên quan với nhau bằng chú thích rõ ràng
- Xuất tất cả tiện ích từ `src/lib/utils.ts`
- Sử dụng named exports để tối ưu hóa kích thước bundle

## Định nghĩa kiểu dữ liệu

Các kiểu dữ liệu chung được định nghĩa trong `src/types.ts`.
