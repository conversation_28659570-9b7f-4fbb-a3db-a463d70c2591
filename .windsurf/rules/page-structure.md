---
trigger: model_decision
description: Quy tắc tạo page mới với tách biệt logic và giao diện
---

# Quy tắc tạo Page mới

## Nguyên tắc

1. **Tách biệt Logic và UI**: Logic trong hooks, UI trong components
2. **Chia nhỏ Component**: <PERSON><PERSON><PERSON> sử dụng tối đa, <PERSON>u tiên shadcn-ui
3. **State Management**: Không hard code, dùng state cho danh sách

## C<PERSON>u trúc thư mục

```
PAGE_NAME/
├── page.tsx              # Entry point
├── _components/          # UI components
├── _hooks/              # Custom hooks (logic)
├── _lib/                # API, validation, utils
└── _types/              # TypeScript types
```

## Template

### page.tsx

```tsx
import { PageContent } from "./_components";

export default function PageName() {
  return <PageContent />;
}
```

### \_components/page-content.tsx

```tsx
"use client";
import { usePageData } from "../_hooks";
import { Button } from "@/components/ui/button";

export function PageContent() {
  const { data, loading, addItem } = usePageData();

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      <Button onClick={() => addItem(newItem)}>Add</Button>
      {data.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  );
}
```

### \_hooks/use-page-data.ts

```tsx
"use client";
import { useState, useEffect } from "react";

export function usePageData() {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);

  const addItem = item => setData(prev => [...prev, item]);
  const updateItem = (id, updates) =>
    setData(prev =>
      prev.map(item => (item.id === id ? { ...item, ...updates } : item))
    );
  const removeItem = id => setData(prev => prev.filter(item => item.id !== id));

  return { data, loading, addItem, updateItem, removeItem };
}
```

## Checklist

- [ ] Logic tách vào hooks
- [ ] Component chia nhỏ, tái sử dụng
- [ ] Dùng shadcn-ui components
- [ ] State management cho danh sách
- [ ] Loading/error states
