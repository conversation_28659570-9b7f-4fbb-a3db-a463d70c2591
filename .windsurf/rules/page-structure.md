---
trigger: model_decision
description: Quy tắc tạo page mới với tách biệt logic và giao diện
---

# Quy tắc tạo Page mới

## Nguyên tắc cơ bản

### 1. Tách biệt rõ ràng Logic và Giao diện

- **KHÔNG** để logic và giao diện chung 1 file
- Logic nghiệp vụ đặt trong custom hooks hoặc utility functions
- Component chỉ chứa JSX và state management cơ bản
- Tách biệt data fetching, validation, và business logic

### 2. Ưu tiên tái cấu trúc và chia nhỏ component

- Chia nhỏ các thành phần để tái sử dụng tối đa
- Tạo component riêng cho từng phần có thể tái sử dụng
- Ưu tiên sử dụng các component từ `@/components/ui/` (shadcn-ui)
- Tạo component wrapper khi cần customize shadcn-ui components

### 3. Qu<PERSON>n lý danh sách bằng State

- **KHÔNG** hard code danh sách
- Sử dụng `useState` hoặc `useReducer` để quản lý danh sách
- Implement CRUD operations cho danh sách
- Sử dụng proper loading và error states

## Cấu trúc thư mục cho Page mới

```
src/app/[lang]/(dashboard-layout)/PAGE_NAME/
├── page.tsx                    # Entry point - chỉ chứa layout và composition
├── _components/                # Components riêng cho page này
│   ├── page-header.tsx        # Header của page
│   ├── page-content.tsx       # Nội dung chính
│   ├── data-table.tsx         # Bảng dữ liệu (nếu có)
│   ├── form-dialog.tsx        # Dialog form (nếu có)
│   └── index.ts               # Export tất cả components
├── _hooks/                     # Custom hooks cho page này
│   ├── use-page-data.ts       # Hook quản lý data
│   ├── use-page-actions.ts    # Hook quản lý actions
│   └── index.ts               # Export tất cả hooks
├── _lib/                       # Utilities riêng cho page
│   ├── validations.ts         # Schema validation
│   ├── api.ts                 # API calls
│   └── utils.ts               # Helper functions
└── _types/                     # Types riêng cho page
    └── index.ts               # Type definitions
```

## Template cho Page mới

### 1. page.tsx (Entry Point)

```tsx
import { Suspense } from "react";
import { PageHeader, PageContent } from "./_components";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

export default function PageName() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <PageHeader />
      <Suspense fallback={<LoadingSpinner />}>
        <PageContent />
      </Suspense>
    </div>
  );
}
```

### 2. \_components/page-content.tsx (Main Content)

```tsx
"use client";

import { usePageData, usePageActions } from "../_hooks";
import { DataTable, FormDialog } from "./";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

export function PageContent() {
  const { data, loading, error } = usePageData();
  const { openCreateDialog } = usePageActions();

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Danh sách</h2>
        <Button onClick={openCreateDialog}>
          <Plus className="w-4 h-4 mr-2" />
          Thêm mới
        </Button>
      </div>

      <DataTable data={data} />
      <FormDialog />
    </div>
  );
}
```

### 3. \_hooks/use-page-data.ts (Data Management)

```tsx
"use client";

import { useState, useEffect } from "react";
import { fetchPageData } from "../_lib/api";
import type { PageData } from "../_types";

export function usePageData() {
  const [data, setData] = useState<PageData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const result = await fetchPageData();
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  const addItem = (item: PageData) => {
    setData(prev => [...prev, item]);
  };

  const updateItem = (id: string, updates: Partial<PageData>) => {
    setData(prev =>
      prev.map(item => (item.id === id ? { ...item, ...updates } : item))
    );
  };

  const removeItem = (id: string) => {
    setData(prev => prev.filter(item => item.id !== id));
  };

  return {
    data,
    loading,
    error,
    loadData,
    addItem,
    updateItem,
    removeItem,
  };
}
```

## Checklist khi tạo Page mới

- [ ] Tách biệt logic vào custom hooks
- [ ] Chia nhỏ component thành các phần tái sử dụng
- [ ] Sử dụng shadcn-ui components từ `@/components/ui/`
- [ ] Quản lý danh sách bằng state (không hard code)
- [ ] Implement loading và error states
- [ ] Tạo proper TypeScript types
- [ ] Validation schema cho forms
- [ ] Responsive design với Tailwind CSS
- [ ] Accessibility (a11y) compliance
- [ ] Performance optimization (memo, callback)

## Best Practices

1. **Component Composition**: Ưu tiên composition over inheritance
2. **Single Responsibility**: Mỗi component/hook có một nhiệm vụ rõ ràng
3. **Reusability**: Tạo component có thể tái sử dụng ở nhiều nơi
4. **Type Safety**: Sử dụng TypeScript đầy đủ
5. **Performance**: Optimize re-renders với React.memo, useMemo, useCallback
6. **Error Handling**: Xử lý lỗi một cách graceful
7. **Loading States**: Hiển thị loading states cho UX tốt hơn
