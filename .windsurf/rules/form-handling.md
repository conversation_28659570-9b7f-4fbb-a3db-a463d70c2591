---
trigger: model_decision
description: <PERSON><PERSON><PERSON><PERSON> dẫn xử lý form trong dự án
---

# X<PERSON> lý Form

## <PERSON><PERSON><PERSON> trú<PERSON> c<PERSON> bản

```tsx
// 1. Import schema từ thư mục schemas
import { FormSchema } from "~/schemas/form-schema";
type FormType = z.infer<typeof FormSchema>;

// 2. Khởi tạo form
export function ExampleForm() {
  const form = useForm<FormType>({
    resolver: zodResolver(FormSchema),
    defaultValues: { email: "", password: "" },
  });
  
  const { isSubmitting, isDirty } = form.formState;
  const isDisabled = isSubmitting || !isDirty;

  // 3. X<PERSON> lý submit
  async function onSubmit(data: FormType) {
    try {
      const res = await fetch("/api/endpoint", {
        method: "POST",
        body: JSON.stringify(data),
      });
      
      if (!res.ok) throw new Error("Có lỗi xảy ra");
      toast({ title: "<PERSON><PERSON><PERSON><PERSON> công!" });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Lỗi",
        description: error instanceof Error ? error.message : undefined,
      });
    }
  }

  // 4. Render form
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input type="email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <ButtonLoading isLoading={isSubmitting} disabled={isDisabled}>
          Gửi
        </ButtonLoading>
      </form>
    </Form>
  );
}
```

## Best Practices

1. **Tạo schema trong thư mục `schemas`**
   ```typescript
   // ~/schemas/form-schema.ts
   import { z } from "zod";
   
   export const FormSchema = z.object({
     username: z.string().min(3, "Tối thiểu 3 ký tự"),
     email: z.string().email("Email không hợp lệ"),
     password: z.string().min(8, "Mật khẩu tối thiểu 8 ký tự"),
   });
   ```

2. **Xử lý lỗi từ API**
   ```typescript
   if (errorData.issues) {
     errorData.issues.forEach((issue) => {
       form.setError(issue.path[0], {
         type: "manual",
         message: issue.message,
       });
     });
   }
   ```

3. **Tối ưu**
   - Dùng `useCallback` cho handler
   - `shouldUnregister: true` để giảm state
   - Validate cả client và server

4. **Đa ngôn ngữ**
   ```typescript
   z.string().email("form.errors.email")
   ```
