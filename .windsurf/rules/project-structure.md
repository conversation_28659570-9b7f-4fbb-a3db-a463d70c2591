---
trigger: model_decision
description: Hướng dẫn cấu trúc và tổ chức dự án
---

# Cấu trúc dự án

## Cấu trúc chính

- Dự án sử dụng Next.js với App Router
- Mã nguồn chính nằm trong thư mục `src/`
- Components UI tái sử dụng trong `components/ui/`
- Các trang nằm trong thư mục `app/[lang]` để hỗ trợ đa ngôn ngữ

```tree
.
├── public/                  # Tài nguyên tĩnh
│   ├── images/              # Hình ảnh
│   │   ├── avatars/         # Ảnh đại diện
│   │   ├── icons/           # Biểu tượng
│   │   ├── illustrations/   # Hình minh họa
│   │   └── misc/            # Hình ảnh khác
│
├── src/
│   ├── app/                 # Next.js App Router
│   │   └── [lang]/          # Đa ngôn ngữ
│   │       ├── (dashboard-layout)/  # Layout chính
│   │       │   ├── (desgin-system)/ # Hệ thống thiết kế
│   │       │   │   ├── forms/       # Các mẫu form
│   │       │   │   └── ...
│   │       │   └── ...
│   │       └── api/        # API Routes
│   │
│   ├── components/          # Các component
│   │   ├── pages/           # Component trang
│   │   ├── ui/              # Component UI tái sử dụng
│   │   └── ...
│   │
│   ├── configs/            # Cấu hình ứng dụng
│   ├── contexts/            # React Contexts
│   ├── data/                # Dữ liệu tĩnh
│   │   └── dictionaries/    # Dữ liệu đa ngôn ngữ
│   │
│   ├── hooks/              # Custom hooks
│   ├── lib/                 # Thư viện tiện ích
│   ├── providers/           # Providers cho ứng dụng
│   ├── schemas/             # Schema validation
│   ├── server/              # Server-side code
│   │   └── auth/           # Xác thực
│   │
│   └── styles/             # Global styles
│
├── .env.example             # Biến môi trường
└── ...
```

## Quy ước đặt tên và tổ chức

- Sử dụng **kebab-case** cho tên thư mục (ví dụ: `form-layouts`)
- Sử dụng **kebab-case** cho tên component (ví dụ: `button.tsx`)
  \_ Nếu 1 component được sử dụng ở nhiều nơi hãy đặt nó trong `components`
- Các component UI tái sử dụng và shadcn đặt trong `components/ui/`
- Các component cụ thể cho trang đặt trong thư mục `_components` của trang đó
- Tách biệt rõ ràng giữa logic và giao diện

## Tạo trang mới

1. Tạo thư mục mới trong `app/[lang]/(dashboard-layout)/`
2. Tạo file `page.tsx` cho nội dung chính
3. Tạo thư mục `_components` cho các component riêng của trang (nếu cần)
4. Sử dụng Server Components mặc định để tối ưu hiệu năng
5. Sử dụng Client Components khi cần tương tác với người dùng

## Component UI

- Các component UI được xây dựng dựa trên Radix UI và Tailwind CSS
- Mỗi component nên có file riêng và export rõ ràng
- Sử dụng TypeScript cho type safety
- Tài liệu hóa props và cách sử dụng

## Quản lý trạng thái

- Sử dụng React Context cho các state toàn cục
- Sử dụng `useState` và `useReducer` cho state cục bộ
- Tách biệt logic nghiệp vụ khỏi component khi cần thiết

## Đa ngôn ngữ

- Hỗ trợ đa ngôn ngữ thông qua thư mục `[lang]`
- Dữ liệu đa ngôn ngữ được lưu trong `src/data/dictionaries/`
- Sử dụng thư viện `next-intl` để quản lý ngôn ngữ

## Các hàm tiện ích

- Tạo các hàm tiện ích trong `lib/utils` để logic có thể tái sử dụng
