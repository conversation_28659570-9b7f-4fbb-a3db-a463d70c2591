---
trigger: model_decision
description: Thành phần UI và hướng dẫn tạo kiểu sử dụng Shadcn UI, Radix UI và Tailwind
---

# Hướng dẫn sử dụng UI Components

## Khung UI

- **Shadcn UI** và **Radix UI** làm thư viện component cơ bản
- **Tailwind CSS** cho việc tạo kiểu và bố cục
- **Next.js** cho việc tối ưu hình ảnh với `next/image`
- **TypeScript** cho kiểm tra kiểu dữ liệu chặt chẽ

## Cài đặt thành phần mới

### Cài đặt component từ Shadcn UI

```sh
pnpm dlx shadcn@latest add <component-name>
```

Ví dụ:

```sh
pnpm dlx shadcn@latest add button
```

### Cài đặt thủ công với Radix UI

```sh
pnpm add @radix-ui/react-<component-name>
```

## Quy ước phát triển component

### Tổ chức component

- Mỗi component chính nên được đặt trong thư mục riêng
- Đặt file `index.ts` để export component chính
- Đặt các component con trong thư mục `_components`
- Đặt các kiểu dữ liệu liên quan trong file `types.ts`

### Component với Slot (Radix UI)

Khi sử dụng `Slot` từ `@radix-ui/react-slot`, cần chú ý:

1. Xử lý đúng các sự kiện để tránh xung đột type
2. Sử dụng `React.forwardRef` để đảm bảo ref hoạt động đúng
3. Kết hợp với `asChild` để tái sử dụng component

Ví dụ với Button component:

```tsx
import { Slot } from "@radix-ui/react-slot";
import { ButtonHTMLAttributes, forwardRef } from "react";

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  asChild?: boolean;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(
          "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button };
```

### Component dạng Container

Đối với các component container như `TagsContainer`:

1. Tách biệt logic hiển thị (`TagsContainer`) với logic xử lý dữ liệu
2. Sử dụng `children` để cho phép tùy chỉnh nội dung
3. Đảm bảo hỗ trợ `className` để tùy chỉnh giao diện

Ví dụ:

```tsx
interface TagsContainerProps {
  children: React.ReactNode;
  className?: string;
}

export function TagsContainer({ children, className }: TagsContainerProps) {
  return (
    <div
      className={cn("flex flex-wrap gap-2 p-2 border rounded-md", className)}
    >
      {children}
    </div>
  );
}
```

## Xử lý sự kiện

Đối với các sự kiện như `onBeforeInput`, cần đảm bảo type chính xác:

```tsx
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  // Các props khác
  onBeforeInput?: React.FormEventHandler<HTMLInputElement>;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ onBeforeInput, ...props }, ref) => {
    return <input ref={ref} onBeforeInput={onBeforeInput} {...props} />;
  }
);
```

## Best Practices

1. **Type Safety**: Luôn định nghĩa đầy đủ kiểu dữ liệu cho props và state
2. **Component Composition**: Ưu tiên sử dụng composition thay vì kế thừa
3. **Reusability**: Tạo các component có thể tái sử dụng với props linh hoạt
4. **Performance**: Sử dụng `React.memo`, `useMemo`, `useCallback` khi cần thiết

## Tài liệu tham khảo

- [Shadcn UI Documentation](https://ui.shadcn.com/)
- [Radix UI Documentation](https://www.radix-ui.com/)
- [Tailwind CSS Documentation](https://tailwindcss.com/)
- [React TypeScript Cheatsheet](https://react-typescript-cheatsheet.netlify.app/)
