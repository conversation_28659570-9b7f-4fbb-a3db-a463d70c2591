# Admin Dashboard Template

M<PERSON>t template hiện đại cho admin dashboard được xây dựng trên nền tảng T3 Stack, cung cấp giao diện người dùng đẹp mắt và dễ tùy chỉnh.

## 🚀 Tính năng chính

- 🎨 **Giao diện hiện đại** với thiết kế responsive
- 🔧 Hỗ trợ đa ngôn ngữ (i18n)
- 🔐 Xác thực người dùng với NextAuth.js
- 🎯 Component UI tái sử dụng được xây dựng với Shadcn/ui
- ⚡ Tối ưu hiệu năng với Next.js 13+ (App Router)

## 🛠 Công nghệ sử dụng

- [Next.js](https://nextjs.org) - Framework React
- [TypeScript](https://www.typescriptlang.org/) - Ngôn ngữ lập trình
- [Tailwind CSS](https://tailwindcss.com) - Utility-first CSS framework
- [Shadcn/ui](https://ui.shadcn.com/) - Component UI
- [NextAuth.js](https://next-auth.js.org) - X<PERSON>c thực

## 🏗 Cấu trúc thư mục

```
src/
├── app/                    # App Router
├── components/             # Component UI tái sử dụng
│   ├── ui/                 # Shadcn/ui components
│   └── shared/             # Component dùng chung
├── lib/                    # Thư viện và tiện ích
├── public/                 # Tài nguyên tĩnh
└── styles/                 # Global styles
```

## 🚀 Bắt đầu

1. **Cài đặt phụ thuộc**

   ```bash
   npm install
   # hoặc
   yarn
   # hoặc
   pnpm install
   ```

2. **Cấu hình biến môi trường**

   - Sao chép file `.env.example` thành `.env`
   - Điền các biến môi trường cần thiết

3. **Chạy dự án**

   ```bash
   npm run dev
   # hoặc
   yarn dev
   # hoặc
   pnpm dev
   ```

4. **Mở trình duyệt**
   - Truy cập [http://localhost:3000](http://localhost:3000)

## 📝 Tài liệu

- [Tài liệu Next.js](https://nextjs.org/docs)
- [Tài liệu Shadcn/ui](https://ui.shadcn.com/docs)

## 🤝 Đóng góp

Đóng góp của bạn luôn được chào đón! Hãy tạo issue hoặc pull request nếu bạn muốn đóng góp vào dự án.

## 📄 Giấy phép

Dự án này được cấp phép theo giấy phép MIT - xem file [LICENSE](LICENSE) để biết thêm chi tiết.
