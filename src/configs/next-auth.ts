import { PrismaAdapter } from "@auth/prisma-adapter";
import type { DefaultSession, NextAuthConfig } from "next-auth";
import NextAuth from "next-auth";
import type { Adapter } from "next-auth/adapters";
import type { JWT } from "next-auth/jwt";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import { env } from "~/env";

import { db } from "~/server/db";

// Extend NextAuth's Session and User interfaces to include custom properties
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      email?: string | null;
      name?: string | null;
      avatar?: string | null;
      status?: string;
    };
  }

  interface User {
    id?: string;
    email?: string | null;
    name?: string | null;
    avatar?: string | null;
    status?: string;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    email?: string | null;
    name?: string | null;
    avatar?: string | null;
    status?: string;
  }
}

export const authOptions = {
  adapter: PrismaAdapter(db) as Adapter,
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { type: "email" },
        password: { type: "password" },
      },
      authorize: async credentials => {
        if (!credentials) return null;

        try {
          const res = await fetch(`${env.NEXT_PUBLIC_API_URL}/auth/sign-in`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: credentials.email,
              password: credentials.password,
            }),
          });

          const payload = await res.json();

          if (res.status >= 400) {
            throw new Error(payload?.message ?? "An unknown error occurred.");
          }

          return payload;
        } catch (e: unknown) {
          throw new Error(
            e instanceof Error ? e.message : "An unknown error occurred."
          );
        }
      },
    }),
  ],
  pages: {
    signIn: "/sign-in",
  },
  session: {
    strategy: "jwt" as const,
    maxAge: 30 * 24 * 60 * 60,
  },
  callbacks: {
    async jwt({ token, user }: { token: JWT; user?: any }) {
      if (user) {
        token.id = user.id;
        token.name = user.name;
        token.avatar = user.avatar;
        token.email = user.email;
        token.status = user.status;
      }

      return token;
    },
    async session({ session, token }: { session: any; token: JWT }) {
      if (session.user) {
        session.user.id = token.id;
        session.user.name = token.name;
        session.user.avatar = token.avatar;
        session.user.email = token.email;
        token.status = token.status;
      }

      return session;
    },
  },
} satisfies NextAuthConfig;
