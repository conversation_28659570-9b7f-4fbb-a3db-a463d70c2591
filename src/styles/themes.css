.theme-zinc {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.985 0 0);
  --warning: oklch(0.7 0.2 60);
  --warning-foreground: oklch(0.985 0 0);
  --info: oklch(0.6 0.18 260);
  --info-foreground: oklch(0.985 0 0);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.205 0 0);

  @variant dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.145 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.145 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.985 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --destructive-foreground: oklch(0.985 0 0);
    --warning: oklch(0.65 0.2 60);
    --warning-foreground: oklch(0.985 0 0);
    --info: oklch(0.55 0.18 260);
    --info-foreground: oklch(0.985 0 0);
    --border: oklch(0.269 0 0);
    --input: oklch(0.269 0 0);
    --ring: oklch(0.856 0 0);
  }
}
.theme-slate {
  --background: oklch(1 0 0);
  --foreground: oklch(0.2 0.02 255);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.2 0.02 255);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.2 0.02 255);
  --primary: oklch(0.3 0.02 250);
  --primary-foreground: oklch(0.95 0.01 240);
  --secondary: oklch(0.93 0.01 230);
  --secondary-foreground: oklch(0.3 0.02 250);
  --muted: oklch(0.93 0.01 230);
  --muted-foreground: oklch(0.6 0.02 235);
  --accent: oklch(0.93 0.01 230);
  --accent-foreground: oklch(0.3 0.02 250);
  --destructive: oklch(0.65 0.25 25);
  --destructive-foreground: oklch(0.95 0.01 240);
  --warning: oklch(0.7 0.2 60);
  --warning-foreground: oklch(0.95 0.01 240);
  --info: oklch(0.6 0.18 260);
  --info-foreground: oklch(0.95 0.01 240);
  --border: oklch(0.9 0.01 230);
  --input: oklch(0.9 0.01 230);
  --ring: oklch(0.2 0.02 255);

  @variant dark {
    --background: oklch(0.2 0.02 255);
    --foreground: oklch(0.95 0.01 240);
    --card: oklch(0.2 0.02 255);
    --card-foreground: oklch(0.95 0.01 240);
    --popover: oklch(0.2 0.02 255);
    --popover-foreground: oklch(0.95 0.01 240);
    --primary: oklch(0.95 0.01 240);
    --primary-foreground: oklch(0.3 0.02 250);
    --secondary: oklch(0.3 0.03 240);
    --secondary-foreground: oklch(0.95 0.01 240);
    --muted: oklch(0.3 0.03 240);
    --muted-foreground: oklch(0.7 0.02 235);
    --accent: oklch(0.3 0.03 240);
    --accent-foreground: oklch(0.95 0.01 240);
    --destructive: oklch(0.5 0.25 25);
    --destructive-foreground: oklch(0.95 0.01 240);
    --warning: oklch(0.65 0.2 60);
    --warning-foreground: oklch(0.95 0.01 240);
    --info: oklch(0.55 0.18 260);
    --info-foreground: oklch(0.95 0.01 240);
    --border: oklch(0.3 0.03 240);
    --input: oklch(0.3 0.03 240);
    --ring: oklch(0.85 0.02 235);
  }
}
.theme-stone {
  --background: oklch(1 0 0);
  --foreground: oklch(0.2 0.02 70);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.2 0.02 70);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.2 0.02 70);
  --primary: oklch(0.25 0.02 65);
  --primary-foreground: oklch(0.95 0.01 60);
  --secondary: oklch(0.93 0.01 60);
  --secondary-foreground: oklch(0.25 0.02 65);
  --muted: oklch(0.93 0.01 60);
  --muted-foreground: oklch(0.55 0.02 65);
  --accent: oklch(0.93 0.01 60);
  --accent-foreground: oklch(0.25 0.02 65);
  --destructive: oklch(0.65 0.25 25);
  --destructive-foreground: oklch(0.95 0.01 60);
  --warning: oklch(0.7 0.2 60);
  --warning-foreground: oklch(0.95 0.01 60);
  --info: oklch(0.6 0.18 260);
  --info-foreground: oklch(0.95 0.01 60);
  --border: oklch(0.9 0.01 60);
  --input: oklch(0.9 0.01 60);
  --ring: oklch(0.25 0.02 65);
  --radius: 0.95rem;

  @variant dark {
    --background: oklch(0.2 0.02 70);
    --foreground: oklch(0.95 0.01 60);
    --card: oklch(0.2 0.02 70);
    --card-foreground: oklch(0.95 0.01 60);
    --popover: oklch(0.2 0.02 70);
    --popover-foreground: oklch(0.95 0.01 60);
    --primary: oklch(0.95 0.01 60);
    --primary-foreground: oklch(0.25 0.02 65);
    --secondary: oklch(0.3 0.02 60);
    --secondary-foreground: oklch(0.95 0.01 60);
    --muted: oklch(0.3 0.02 60);
    --muted-foreground: oklch(0.65 0.02 65);
    --accent: oklch(0.3 0.02 60);
    --accent-foreground: oklch(0.95 0.01 60);
    --destructive: oklch(0.5 0.25 25);
    --destructive-foreground: oklch(0.95 0.01 60);
    --warning: oklch(0.65 0.2 60);
    --warning-foreground: oklch(0.95 0.01 60);
    --info: oklch(0.55 0.18 260);
    --info-foreground: oklch(0.95 0.01 60);
    --border: oklch(0.3 0.02 60);
    --input: oklch(0.3 0.02 60);
    --ring: oklch(0.85 0.01 65);
  }
}
.theme-gray {
  --background: oklch(1 0 0);
  --foreground: oklch(0.15 0.01 260);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.01 260);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.01 260);
  --primary: oklch(0.25 0.01 260);
  --primary-foreground: oklch(0.98 0 0);
  --secondary: oklch(0.95 0 0);
  --secondary-foreground: oklch(0.25 0.01 260);
  --muted: oklch(0.95 0 0);
  --muted-foreground: oklch(0.55 0.01 260);
  --accent: oklch(0.95 0 0);
  --accent-foreground: oklch(0.25 0.01 260);
  --destructive: oklch(0.65 0.25 25);
  --destructive-foreground: oklch(0.98 0 0);
  --warning: oklch(0.7 0.2 60);
  --warning-foreground: oklch(0.98 0 0);
  --info: oklch(0.6 0.18 260);
  --info-foreground: oklch(0.98 0 0);
  --border: oklch(0.9 0 0);
  --input: oklch(0.9 0 0);
  --ring: oklch(0.25 0.01 260);
  --radius: 0.35rem;

  @variant dark {
    --background: oklch(0.15 0.01 260);
    --foreground: oklch(0.98 0 0);
    --card: oklch(0.15 0.01 260);
    --card-foreground: oklch(0.98 0 0);
    --popover: oklch(0.15 0.01 260);
    --popover-foreground: oklch(0.98 0 0);
    --primary: oklch(0.98 0 0);
    --primary-foreground: oklch(0.25 0.01 260);
    --secondary: oklch(0.25 0.01 260);
    --secondary-foreground: oklch(0.98 0 0);
    --muted: oklch(0.25 0.01 260);
    --muted-foreground: oklch(0.65 0.01 260);
    --accent: oklch(0.25 0.01 260);
    --accent-foreground: oklch(0.98 0 0);
    --destructive: oklch(0.5 0.25 25);
    --destructive-foreground: oklch(0.98 0 0);
    --warning: oklch(0.65 0.2 60);
    --warning-foreground: oklch(0.98 0 0);
    --info: oklch(0.55 0.18 260);
    --info-foreground: oklch(0.98 0 0);
    --border: oklch(0.25 0.01 260);
    --input: oklch(0.25 0.01 260);
    --ring: oklch(0.8 0.01 260);
  }
}
.theme-neutral {
  --background: oklch(1 0 0);
  --foreground: oklch(0.15 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0 0);
  --primary: oklch(0.25 0 0);
  --primary-foreground: oklch(0.98 0 0);
  --secondary: oklch(0.95 0 0);
  --secondary-foreground: oklch(0.25 0 0);
  --muted: oklch(0.95 0 0);
  --muted-foreground: oklch(0.55 0 0);
  --accent: oklch(0.95 0 0);
  --accent-foreground: oklch(0.25 0 0);
  --destructive: oklch(0.65 0.25 25);
  --destructive-foreground: oklch(0.98 0 0);
  --warning: oklch(0.7 0.2 60);
  --warning-foreground: oklch(0.98 0 0);
  --info: oklch(0.6 0.18 260);
  --info-foreground: oklch(0.98 0 0);
  --border: oklch(0.9 0 0);
  --input: oklch(0.9 0 0);
  --ring: oklch(0.25 0 0);

  @variant dark {
    --background: oklch(0.15 0 0);
    --foreground: oklch(0.98 0 0);
    --card: oklch(0.15 0 0);
    --card-foreground: oklch(0.98 0 0);
    --popover: oklch(0.15 0 0);
    --popover-foreground: oklch(0.98 0 0);
    --primary: oklch(0.98 0 0);
    --primary-foreground: oklch(0.25 0 0);
    --secondary: oklch(0.25 0 0);
    --secondary-foreground: oklch(0.98 0 0);
    --muted: oklch(0.25 0 0);
    --muted-foreground: oklch(0.65 0 0);
    --accent: oklch(0.25 0 0);
    --accent-foreground: oklch(0.98 0 0);
    --destructive: oklch(0.5 0.25 25);
    --destructive-foreground: oklch(0.98 0 0);
    --warning: oklch(0.65 0.2 60);
    --warning-foreground: oklch(0.98 0 0);
    --info: oklch(0.55 0.18 260);
    --info-foreground: oklch(0.98 0 0);
    --border: oklch(0.25 0 0);
    --input: oklch(0.25 0 0);
    --ring: oklch(0.8 0 0);
  }
}
.theme-red {
  --background: oklch(1 0 0);
  --foreground: oklch(0.15 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0 0);
  --primary: oklch(0.63 0.26 25);
  --primary-foreground: oklch(0.98 0.02 25);
  --secondary: oklch(0.95 0 0);
  --secondary-foreground: oklch(0.2 0 0);
  --muted: oklch(0.95 0 0);
  --muted-foreground: oklch(0.55 0 0);
  --accent: oklch(0.95 0 0);
  --accent-foreground: oklch(0.2 0 0);
  --destructive: oklch(0.65 0.28 25);
  --destructive-foreground: oklch(0.98 0 0);
  --warning: oklch(0.7 0.2 60);
  --warning-foreground: oklch(0.98 0 0);
  --info: oklch(0.6 0.18 260);
  --info-foreground: oklch(0.98 0 0);
  --border: oklch(0.9 0 0);
  --input: oklch(0.9 0 0);
  --ring: oklch(0.63 0.26 25);
  --radius: 0.4rem;

  @variant dark {
    --background: oklch(0.15 0 0);
    --foreground: oklch(0.98 0 0);
    --card: oklch(0.15 0 0);
    --card-foreground: oklch(0.98 0 0);
    --popover: oklch(0.15 0 0);
    --popover-foreground: oklch(0.98 0 0);
    --primary: oklch(0.63 0.26 25);
    --primary-foreground: oklch(0.98 0.02 25);
    --secondary: oklch(0.25 0 0);
    --secondary-foreground: oklch(0.98 0 0);
    --muted: oklch(0.25 0 0);
    --muted-foreground: oklch(0.65 0 0);
    --accent: oklch(0.25 0 0);
    --accent-foreground: oklch(0.98 0 0);
    --destructive: oklch(0.5 0.28 25);
    --destructive-foreground: oklch(0.98 0 0);
    --warning: oklch(0.65 0.2 60);
    --warning-foreground: oklch(0.98 0 0);
    --info: oklch(0.55 0.18 260);
    --info-foreground: oklch(0.98 0 0);
    --border: oklch(0.25 0 0);
    --input: oklch(0.25 0 0);
    --ring: oklch(0.63 0.26 25);
  }
}
.theme-rose {
  --background: oklch(1 0 0);
  --foreground: oklch(0.15 0.01 260);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.01 260);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.01 260);
  --primary: oklch(0.63 0.28 5);
  --primary-foreground: oklch(0.98 0.02 0);
  --secondary: oklch(0.95 0.01 260);
  --secondary-foreground: oklch(0.2 0.01 260);
  --muted: oklch(0.95 0.01 260);
  --muted-foreground: oklch(0.55 0.01 260);
  --accent: oklch(0.95 0.01 260);
  --accent-foreground: oklch(0.2 0.01 260);
  --destructive: oklch(0.65 0.28 25);
  --destructive-foreground: oklch(0.98 0 0);
  --warning: oklch(0.7 0.2 60);
  --warning-foreground: oklch(0.98 0 0);
  --info: oklch(0.6 0.18 260);
  --info-foreground: oklch(0.98 0 0);
  --border: oklch(0.9 0.01 260);
  --input: oklch(0.9 0.01 260);
  --ring: oklch(0.63 0.28 5);

  @variant dark {
    --background: oklch(0.15 0.02 30);
    --foreground: oklch(0.95 0 0);
    --card: oklch(0.2 0.02 30);
    --card-foreground: oklch(0.95 0 0);
    --popover: oklch(0.15 0 0);
    --popover-foreground: oklch(0.95 0 0);
    --primary: oklch(0.63 0.28 5);
    --primary-foreground: oklch(0.98 0.02 0);
    --secondary: oklch(0.25 0.01 260);
    --secondary-foreground: oklch(0.98 0 0);
    --muted: oklch(0.25 0 0);
    --muted-foreground: oklch(0.65 0.01 260);
    --accent: oklch(0.25 0.02 30);
    --accent-foreground: oklch(0.98 0 0);
    --destructive: oklch(0.5 0.28 25);
    --destructive-foreground: oklch(0.98 0.02 0);
    --warning: oklch(0.65 0.2 60);
    --warning-foreground: oklch(0.98 0 0);
    --info: oklch(0.55 0.18 260);
    --info-foreground: oklch(0.98 0 0);
    --border: oklch(0.25 0.01 260);
    --input: oklch(0.25 0.01 260);
    --ring: oklch(0.63 0.28 5);
  }
}
.theme-orange {
  --background: oklch(1 0 0);
  --foreground: oklch(0.15 0.02 50);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.02 50);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.02 50);
  --primary: oklch(0.7 0.2 60);
  --primary-foreground: oklch(0.98 0.01 80);
  --secondary: oklch(0.95 0.01 80);
  --secondary-foreground: oklch(0.2 0.02 50);
  --muted: oklch(0.95 0.01 80);
  --muted-foreground: oklch(0.55 0.02 50);
  --accent: oklch(0.95 0.01 80);
  --accent-foreground: oklch(0.2 0.02 50);
  --destructive: oklch(0.65 0.28 25);
  --destructive-foreground: oklch(0.98 0.01 80);
  --warning: oklch(0.7 0.2 60);
  --warning-foreground: oklch(0.98 0.01 80);
  --info: oklch(0.6 0.18 260);
  --info-foreground: oklch(0.98 0.01 80);
  --border: oklch(0.9 0.01 50);
  --input: oklch(0.9 0.01 50);
  --ring: oklch(0.7 0.2 60);

  @variant dark {
    --background: oklch(0.15 0.02 50);
    --foreground: oklch(0.98 0.01 80);
    --card: oklch(0.15 0.02 50);
    --card-foreground: oklch(0.98 0.01 80);
    --popover: oklch(0.15 0.02 50);
    --popover-foreground: oklch(0.98 0.01 80);
    --primary: oklch(0.7 0.2 45);
    --primary-foreground: oklch(0.98 0.01 80);
    --secondary: oklch(0.25 0.02 40);
    --secondary-foreground: oklch(0.98 0.01 80);
    --muted: oklch(0.25 0.02 40);
    --muted-foreground: oklch(0.65 0.02 50);
    --accent: oklch(0.25 0.02 40);
    --accent-foreground: oklch(0.98 0.01 80);
    --destructive: oklch(0.5 0.28 25);
    --destructive-foreground: oklch(0.98 0.01 80);
    --warning: oklch(0.65 0.2 60);
    --warning-foreground: oklch(0.98 0.01 80);
    --info: oklch(0.55 0.18 260);
    --info-foreground: oklch(0.98 0.01 80);
    --border: oklch(0.25 0.02 40);
    --input: oklch(0.25 0.02 40);
    --ring: oklch(0.7 0.2 45);
  }
}
.theme-green {
  --background: oklch(1 0 0);
  --foreground: oklch(0.15 0.01 140);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.01 140);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.01 140);
  --primary: oklch(0.6 0.18 145);
  --primary-foreground: oklch(0.98 0.01 0);
  --secondary: oklch(0.95 0.01 140);
  --secondary-foreground: oklch(0.2 0.01 140);
  --muted: oklch(0.95 0.01 140);
  --muted-foreground: oklch(0.55 0.01 140);
  --accent: oklch(0.95 0.01 140);
  --accent-foreground: oklch(0.2 0.01 140);
  --destructive: oklch(0.65 0.28 25);
  --destructive-foreground: oklch(0.98 0 0);
  --warning: oklch(0.7 0.2 60);
  --warning-foreground: oklch(0.98 0 0);
  --info: oklch(0.6 0.18 260);
  --info-foreground: oklch(0.98 0 0);
  --border: oklch(0.9 0.01 140);
  --input: oklch(0.9 0.01 140);
  --ring: oklch(0.6 0.18 145);

  @variant dark {
    --background: oklch(0.15 0.01 140);
    --foreground: oklch(0.98 0 0);
    --card: oklch(0.2 0.01 140);
    --card-foreground: oklch(0.98 0 0);
    --popover: oklch(0.15 0.01 140);
    --popover-foreground: oklch(0.98 0 0);
    --primary: oklch(0.65 0.18 145);
    --primary-foreground: oklch(0.2 0.01 140);
    --secondary: oklch(0.25 0.01 140);
    --secondary-foreground: oklch(0.98 0 0);
    --muted: oklch(0.25 0.01 140);
    --muted-foreground: oklch(0.65 0.01 140);
    --accent: oklch(0.25 0.01 140);
    --accent-foreground: oklch(0.98 0 0);
    --destructive: oklch(0.5 0.28 25);
    --destructive-foreground: oklch(0.98 0 0);
    --warning: oklch(0.65 0.2 60);
    --warning-foreground: oklch(0.98 0 0);
    --info: oklch(0.55 0.18 260);
    --info-foreground: oklch(0.98 0 0);
    --border: oklch(0.25 0.01 140);
    --input: oklch(0.25 0.01 140);
    --ring: oklch(0.65 0.18 145);
  }
}
.theme-blue {
  --background: oklch(1 0 0);
  --foreground: oklch(0.15 0.03 260);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.03 260);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.03 260);
  --primary: oklch(0.6 0.18 260);
  --primary-foreground: oklch(0.98 0.01 240);
  --secondary: oklch(0.95 0.01 240);
  --secondary-foreground: oklch(0.2 0.03 260);
  --muted: oklch(0.95 0.01 240);
  --muted-foreground: oklch(0.55 0.03 260);
  --accent: oklch(0.95 0.01 240);
  --accent-foreground: oklch(0.2 0.03 260);
  --destructive: oklch(0.65 0.28 25);
  --destructive-foreground: oklch(0.98 0.01 240);
  --warning: oklch(0.7 0.2 60);
  --warning-foreground: oklch(0.98 0.01 240);
  --info: oklch(0.6 0.18 260);
  --info-foreground: oklch(0.98 0.01 240);
  --border: oklch(0.9 0.01 240);
  --input: oklch(0.9 0.01 240);
  --ring: oklch(0.6 0.18 260);

  @variant dark {
    --background: oklch(0.15 0.03 260);
    --foreground: oklch(0.98 0.01 240);
    --card: oklch(0.15 0.03 260);
    --card-foreground: oklch(0.98 0.01 240);
    --popover: oklch(0.15 0.03 260);
    --popover-foreground: oklch(0.98 0.01 240);
    --primary: oklch(0.65 0.18 255);
    --primary-foreground: oklch(0.2 0.03 260);
    --secondary: oklch(0.25 0.03 255);
    --secondary-foreground: oklch(0.98 0.01 240);
    --muted: oklch(0.25 0.03 255);
    --muted-foreground: oklch(0.65 0.03 260);
    --accent: oklch(0.25 0.03 255);
    --accent-foreground: oklch(0.98 0.01 240);
    --destructive: oklch(0.5 0.28 25);
    --destructive-foreground: oklch(0.98 0.01 240);
    --warning: oklch(0.65 0.2 60);
    --warning-foreground: oklch(0.98 0.01 240);
    --info: oklch(0.55 0.18 260);
    --info-foreground: oklch(0.98 0.01 240);
    --border: oklch(0.25 0.03 255);
    --input: oklch(0.25 0.03 255);
    --ring: oklch(0.6 0.18 255);
  }
}
.theme-yellow {
  --background: oklch(1 0 0);
  --foreground: oklch(0.15 0.02 80);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.02 80);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.02 80);
  --primary: oklch(0.85 0.2 85);
  --primary-foreground: oklch(0.3 0.05 80);
  --secondary: oklch(0.95 0.01 85);
  --secondary-foreground: oklch(0.2 0.02 80);
  --muted: oklch(0.95 0.01 85);
  --muted-foreground: oklch(0.55 0.02 80);
  --accent: oklch(0.95 0.01 85);
  --accent-foreground: oklch(0.2 0.02 80);
  --destructive: oklch(0.65 0.28 25);
  --destructive-foreground: oklch(0.98 0.01 85);
  --warning: oklch(0.7 0.2 60);
  --warning-foreground: oklch(0.98 0.01 85);
  --info: oklch(0.6 0.18 260);
  --info-foreground: oklch(0.98 0.01 85);
  --border: oklch(0.9 0.01 80);
  --input: oklch(0.9 0.01 80);
  --ring: oklch(0.15 0.02 80);
  --radius: 0.95rem;

  @variant dark {
    --background: oklch(0.15 0.02 80);
    --foreground: oklch(0.98 0.01 85);
    --card: oklch(0.15 0.02 80);
    --card-foreground: oklch(0.98 0.01 85);
    --popover: oklch(0.15 0.02 80);
    --popover-foreground: oklch(0.98 0.01 85);
    --primary: oklch(0.85 0.2 85);
    --primary-foreground: oklch(0.3 0.05 80);
    --secondary: oklch(0.25 0.02 75);
    --secondary-foreground: oklch(0.98 0.01 85);
    --muted: oklch(0.25 0.02 75);
    --muted-foreground: oklch(0.65 0.02 80);
    --accent: oklch(0.25 0.02 75);
    --accent-foreground: oklch(0.98 0.01 85);
    --destructive: oklch(0.5 0.28 25);
    --destructive-foreground: oklch(0.98 0.01 85);
    --warning: oklch(0.65 0.2 60);
    --warning-foreground: oklch(0.98 0.01 85);
    --info: oklch(0.55 0.18 260);
    --info-foreground: oklch(0.98 0.01 85);
    --border: oklch(0.25 0.02 75);
    --input: oklch(0.25 0.02 75);
    --ring: oklch(0.6 0.2 85);
  }
}
.theme-violet {
  --background: oklch(1 0 0);
  --foreground: oklch(0.15 0.03 280);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.03 280);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.03 280);
  --primary: oklch(0.65 0.25 290);
  --primary-foreground: oklch(0.98 0.01 280);
  --secondary: oklch(0.95 0.01 280);
  --secondary-foreground: oklch(0.2 0.03 280);
  --muted: oklch(0.95 0.01 280);
  --muted-foreground: oklch(0.55 0.03 280);
  --accent: oklch(0.95 0.01 280);
  --accent-foreground: oklch(0.2 0.03 280);
  --destructive: oklch(0.65 0.28 25);
  --destructive-foreground: oklch(0.98 0.01 280);
  --warning: oklch(0.7 0.2 60);
  --warning-foreground: oklch(0.98 0.01 280);
  --info: oklch(0.6 0.18 260);
  --info-foreground: oklch(0.98 0.01 280);
  --border: oklch(0.9 0.01 280);
  --input: oklch(0.9 0.01 280);
  --ring: oklch(0.65 0.25 290);

  @variant dark {
    --background: oklch(0.15 0.03 280);
    --foreground: oklch(0.98 0.01 280);
    --card: oklch(0.15 0.03 280);
    --card-foreground: oklch(0.98 0.01 280);
    --popover: oklch(0.15 0.03 280);
    --popover-foreground: oklch(0.98 0.01 280);
    --primary: oklch(0.65 0.25 290);
    --primary-foreground: oklch(0.98 0.01 280);
    --secondary: oklch(0.25 0.03 280);
    --secondary-foreground: oklch(0.98 0.01 280);
    --muted: oklch(0.25 0.03 280);
    --muted-foreground: oklch(0.65 0.03 280);
    --accent: oklch(0.25 0.03 280);
    --accent-foreground: oklch(0.98 0.01 280);
    --destructive: oklch(0.5 0.28 25);
    --destructive-foreground: oklch(0.98 0.01 280);
    --warning: oklch(0.65 0.2 60);
    --warning-foreground: oklch(0.98 0.01 280);
    --info: oklch(0.55 0.18 260);
    --info-foreground: oklch(0.98 0.01 280);
    --border: oklch(0.25 0.03 280);
    --input: oklch(0.25 0.03 280);
    --ring: oklch(0.65 0.25 290);
  }
}
.radius-0 {
  --radius: 0;
}
.radius-0\.3 {
  --radius: 0.3rem;
}
.radius-0\.5 {
  --radius: 0.5rem;
}
.radius-0\.75 {
  --radius: 0.75rem;
}
.radius-1 {
  --radius: 1rem;
}
