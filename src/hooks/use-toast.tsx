"use client";

import { toast as sonnerToast } from "sonner";

import type { ReactNode } from "react";

type ToastProps = {
  variant?: "default" | "destructive" | "success" | "info" | "warning";
  title?: ReactNode;
  description?: ReactNode;
  action?:
    | {
        label: ReactNode;
        onClick: () => void;
      }
    | ReactNode;
  duration?: number;
  id?: string;
  onOpenChange?: (open: boolean) => void;
  onDismiss?: () => void;
};

const variantToType = {
  default: "default",
  destructive: "error",
  success: "success",
  info: "info",
  warning: "warning",
} as const;

function toast(props: ToastProps) {
  const {
    variant = "default",
    title,
    description,
    action,
    duration,
    id,
    onDismiss,
  } = props;

  // Chuyển đổi variant sang type tương ứng trong Sonner
  const type = variantToType[variant] as
    | "success"
    | "error"
    | "info"
    | "warning"
    | "default";

  const toastId = sonnerToast(title as string, {
    id,
    description,
    action,
    duration,
    onDismiss,
    // Không cần truyền type nếu là default
    ...(type !== "default" ? { type } : {}),
  });

  return {
    id: toastId,
    dismiss: () => sonnerToast.dismiss(toastId),
    update: (props: ToastProps) => {
      const { description, action } = props;

      // Sonner không hỗ trợ cập nhật type, chỉ có thể cập nhật nội dung
      sonnerToast.message(toastId, {
        description,
        action,
      });
    },
  };
}

function useToast() {
  return {
    toast,
    dismiss: (toastId?: string) => {
      if (toastId) {
        sonnerToast.dismiss(toastId);
      } else {
        sonnerToast.dismiss();
      }
    },
  };
}

export { toast, useToast };
