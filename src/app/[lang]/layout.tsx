import { Cairo, Lato } from "next/font/google";
import { auth } from "~/server/auth";

import { i18n } from "~/configs/i18n";
import { cn } from "~/lib/utils";

import "~/styles/globals.css";

import { Providers } from "~/providers";

import { env } from "~/env";

import type { Metadata } from "next";
import type { ReactNode } from "react";
import type { LocaleType } from "~/types";

import { Toaster as Sonner } from "~/components/ui/sonner";

export const metadata: Metadata = {
  title: {
    template: "%s | HHco",
    default: "HHco",
  },
  description: "",
  metadataBase: new URL(env.NEXT_PUBLIC_BASE_URL as string),
};

const latoFont = Lato({
  subsets: ["latin"],
  weight: ["100", "300", "400", "700", "900"],
  style: ["normal", "italic"],
  variable: "--font-lato",
});
const cairoFont = Cairo({
  subsets: ["arabic"],
  weight: ["400", "700"],
  style: ["normal"],
  variable: "--font-cairo",
});

export default async function RootLayout(props: {
  children: ReactNode;
  params: Promise<{ lang: LocaleType }>;
}) {
  const params = await props.params;

  const { children } = props;

  const session = await auth();
  const direction = i18n.localeDirection[params.lang];

  return (
    <html lang={params.lang} dir={direction} suppressHydrationWarning>
      <body
        className={cn(
          "[&:lang(en)]:font-lato [&:lang(ar)]:font-cairo", // Set font styles based on the language
          "bg-background text-foreground antialiased overscroll-none", // Set background, text, , anti-aliasing styles, and overscroll behavior
          latoFont.variable, // Include Lato font variable
          cairoFont.variable // Include Cairo font variable
        )}
      >
        <Providers locale={params.lang} direction={direction} session={session}>
          {children}
          <Sonner />
        </Providers>
      </body>
    </html>
  );
}
