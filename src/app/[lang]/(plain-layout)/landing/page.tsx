'use client';

import { useEffect } from 'react';
import { Header } from './_components/header';
import { HeroSection } from './_components/hero-section';
import { FeaturesSection } from './_components/features-section';
import { HowItWorks } from './_components/how-it-works';
import { Testimonials } from './_components/testimonials';
import { Pricing } from './_components/pricing';
import { FAQ } from './_components/faq';
import { CtaSection } from './_components/cta-section';
import { Footer } from './_components/footer';

export default function LandingPage() {
  // Xử lý smooth scroll cho các liên kết nội bộ
  useEffect(() => {
    const handleAnchorClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const link = target.closest('a[href^="#"]') as HTMLAnchorElement;
      
      if (!link) return;
      
      const href = link.getAttribute('href');
      if (!href || href === '#') return;
      
      e.preventDefault();
      
      const targetId = href.substring(1);
      const targetElement = document.getElementById(targetId);
      
      if (targetElement) {
        window.scrollTo({
          top: targetElement.offsetTop - 100, // Điều chỉnh vị trí scroll
          behavior: 'smooth',
        });
        
        // Cập nhật URL mà không làm mới trang
        window.history.pushState(null, '', href);
      }
    };
    
    document.addEventListener('click', handleAnchorClick);
    
    return () => {
      document.removeEventListener('click', handleAnchorClick);
    };
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main>
        <HeroSection />
        <FeaturesSection />
        <HowItWorks />
        <Testimonials />
        <Pricing />
        <FAQ />
        <CtaSection />
      </main>
      <Footer />
    </div>
  );
}
