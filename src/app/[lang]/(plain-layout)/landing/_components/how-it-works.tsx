'use client';

import { CheckCircle2, Code, Settings, Zap } from 'lucide-react';

export function HowItWorks() {
  const steps = [
    {
      icon: <Settings className="h-8 w-8 text-blue-500" />,
      title: 'Thiết lập dễ dàng',
      description: '<PERSON>à<PERSON> đặt nhanh chóng trong vài phút với hướng dẫn từng bước.'
    },
    {
      icon: <Code className="h-8 w-8 text-green-500" />,
      title: 'Tích hợp đơn giản',
      description: 'Kết nối với các công cụ yêu thích của bạn một cách dễ dàng.'
    },
    {
      icon: <Zap className="h-8 w-8 text-purple-500" />,
      title: 'Khởi động ngay',
      description: 'B<PERSON>t đầu sử dụng ngay lập tức với các mẫu có sẵn.'
    }
  ];

  return (
    <section id="how-it-works" className="py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-3xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Cách hoạt động
          </h2>
          <p className="mt-4 text-lg text-gray-600">
            Chỉ với 3 bước đơn giản để bắt đầu sử dụng nền tảng của chúng tôi
          </p>
        </div>

        <div className="mt-16">
          <div className="relative">
            <div className="absolute left-1/2 top-0 h-full w-0.5 -translate-x-1/2 bg-gradient-to-b from-blue-500 via-purple-500 to-green-500" />
            <div className="space-y-16">
              {steps.map((step, index) => (
                <div key={index} className="relative">
                  <div className="absolute left-1/2 -ml-4 flex h-8 w-8 items-center justify-center rounded-full bg-white ring-8 ring-gray-50">
                    <span className="flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                      {step.icon}
                    </span>
                  </div>
                  <div className={`ml-16 ${index % 2 === 0 ? 'text-right' : 'text-left'}`}>
                    <h3 className="text-xl font-semibold text-gray-900">{step.title}</h3>
                    <p className="mt-2 text-gray-600">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
