'use client';

import { useState } from 'react';
import { ChevronDown } from 'lucide-react';

const faqs = [
  {
    question: 'Là<PERSON> thế nào để bắt đầu sử dụng nền tảng?',
    answer:
      '<PERSON><PERSON>n có thể bắt đầu bằng cách đăng ký tài khoản miễn phí. Sau khi đăng ký, bạn sẽ được hướng dẫn từng bước để thiết lập tài khoản và bắt đầu sử dụng các tính năng cơ bản.',
  },
  {
    question: '<PERSON><PERSON> bản dùng thử miễn phí không?',
    answer:
      '<PERSON><PERSON>, chúng tôi cung cấp bản dùng thử miễn phí 14 ngày cho tất cả các gói dịch vụ. Bạn có thể dùng thử đầy đủ tính năng mà không cần cung cấp thông tin thẻ tín dụng.',
  },
  {
    question: '<PERSON><PERSON> những phương thức thanh toán nào?',
    answer:
      '<PERSON>úng tôi chấp nhận thanh toán qua thẻ tín dụng (Visa, Master<PERSON>ard, American Express), PayPal và chuyển khoản ngân hàng.',
  },
  {
    question: 'Làm thế nào để nâng cấp gói dịch vụ?',
    answer:
      'Bạn có thể nâng cấp gói dịch vụ bất cứ lúc nào từ bảng điều khiển tài khoản. Sự thay đổi sẽ có hiệu lực ngay lập tức và bạn sẽ được tính phí chênh lệch theo tỷ lệ.',
  },
  {
    question: 'Có hỗ trợ khách hàng không?',
    answer:
      'Có, chúng tôi cung cấp hỗ trợ khách hàng 24/7 thông qua trò chuyện trực tiếp, email và điện thoại. Đội ngũ hỗ trợ của chúng tôi luôn sẵn sàng giúp đỡ bạn.',
  },
];

export function FAQ() {
  return (
    <section id="faq" className="py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-4xl">
          <div className="text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Câu hỏi thường gặp
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Tìm câu trả lời cho những thắc mắc phổ biến
            </p>
          </div>

          <div className="mt-12 space-y-4">
            {faqs.map((faq, index) => (
              <FAQItem key={index} question={faq.question} answer={faq.answer} />
            ))}
          </div>

          <div className="mt-12 text-center">
            <p className="text-gray-600">
              Vẫn còn thắc mắc?{' '}
              <a
                href="#contact"
                className="font-medium text-blue-600 hover:text-blue-500"
              >
                Liên hệ với chúng tôi
              </a>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

function FAQItem({ question, answer }: { question: string; answer: string }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="overflow-hidden rounded-lg border border-gray-200">
      <button
        className="flex w-full items-center justify-between p-6 text-left"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-lg font-medium text-gray-900">{question}</span>
        <ChevronDown
          className={`h-5 w-5 text-gray-500 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>
      <div
        className={`px-6 pb-6 pt-0 transition-all duration-200 ${
          isOpen ? 'block' : 'hidden'
        }`}
      >
        <p className="text-gray-600">{answer}</p>
      </div>
    </div>
  );
}
