import { But<PERSON> } from "~/components/ui/button";
import { Rocket } from "lucide-react";
import Link from "next/link";

export function HeroSection() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-b from-blue-50 to-white">
      <div className="container mx-auto px-4 py-24 md:py-32">
        <div className="mx-auto max-w-4xl text-center">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
            Giải pháp quản lý dành cho doanh nghiệp của bạn
          </h1>
          <p className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-600">
            Nền tảng quản lý toàn diện giúp doanh nghiệp của bạn vận hành hiệu quả hơn, tiết kiệm thời gian và chi phí.
          </p>
          <div className="mt-10 flex items-center justify-center gap-x-6">
            <Button size="lg" asChild>
              <Link href="/auth/register">
                <Rocket className="mr-2 h-4 w-4" />
                Bắt đầu ngay
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="#features">
                Tìm hiểu thêm
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
