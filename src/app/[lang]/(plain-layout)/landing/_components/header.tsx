'use client';

import Link from 'next/link';
import { Button } from '~/components/ui/button';
import { usePathname } from 'next/navigation';

export function Header() {
  const pathname = usePathname();
  const isActive = (path: string) => pathname === path;

  return (
    <header className="fixed top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-2">
          <Link href="/" className="flex items-center space-x-2">
            <span className="text-xl font-bold">YourLogo</span>
          </Link>
        </div>

        <nav className="hidden items-center space-x-6 md:flex">
          <Link
            href="/landing"
            className={`text-sm font-medium transition-colors hover:text-primary ${isActive('/landing') ? 'text-primary' : 'text-muted-foreground'}`}
          >
            Trang chủ
          </Link>
          <Link
            href="#features"
            className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
          >
            T<PERSON>h năng
          </Link>
          <Link
            href="#how-it-works"
            className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
          >
            Cách hoạt động
          </Link>
          <Link
            href="#pricing"
            className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
          >
            Bảng giá
          </Link>
          <Link
            href="#faq"
            className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
          >
            FAQ
          </Link>
        </nav>

        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/auth/login">Đăng nhập</Link>
          </Button>
          <Button size="sm" asChild>
            <Link href="/auth/register">Đăng ký ngay</Link>
          </Button>
        </div>
      </div>
    </header>
  );
}
