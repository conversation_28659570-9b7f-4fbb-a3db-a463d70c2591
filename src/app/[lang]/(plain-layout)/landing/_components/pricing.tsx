'use client';

import { Button } from '~/components/ui/button';
import { Check, Star } from 'lucide-react';

const plans = [
  {
    name: '<PERSON><PERSON> bản',
    price: '0',
    description: '<PERSON><PERSON> hợp cho cá nhân mới bắt đầu',
    features: [
      'Tối đa 5 dự án',
      '1GB dung lượng lưu trữ',
      'Hỗ trợ cơ bản',
      'Báo cáo cơ bản',
    ],
    buttonText: 'Bắt đầu miễn phí',
    popular: false,
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON> nghiệp',
    price: '29',
    description: '<PERSON><PERSON>n hảo cho các nhóm nhỏ',
    features: [
      'Không giới hạn dự án',
      '10GB dung lượng lưu trữ',
      'Hỗ trợ ưu tiên',
      'Báo cáo nâng cao',
      'Xuất dữ liệu',
    ],
    buttonText: '<PERSON><PERSON>ng thử miễn phí',
    popular: true,
  },
  {
    name: '<PERSON><PERSON><PERSON> nghiệp',
    price: '99',
    description: '<PERSON><PERSON><PERSON>i pháp toàn diện cho doanh nghiệp',
    features: [
      'Không giới hạn dự án',
      'Dung lượng không giới hạn',
      'Hỗ trợ 24/7',
      'Báo cáo tùy chỉnh',
      'Tích hợp API',
      'Đào tạo riêng',
    ],
    buttonText: 'Liên hệ báo giá',
    popular: false,
  },
];

export function Pricing() {
  return (
    <section id="pricing" className="py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Bảng giá linh hoạt
          </h2>
          <p className="mt-4 text-lg text-gray-600">
            Chọn gói dịch vụ phù hợp với nhu cầu của bạn
          </p>
        </div>

        <div className="mt-16 grid gap-8 md:grid-cols-3">
          {plans.map((plan, index) => (
            <div
              key={index}
              className={`relative rounded-2xl border bg-white p-8 shadow-sm transition-all hover:shadow-md ${
                plan.popular ? 'border-2 border-blue-500' : 'border-gray-200'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                  <div className="flex items-center rounded-full bg-blue-500 px-4 py-1 text-xs font-medium text-white">
                    <Star className="mr-1 h-3 w-3 fill-white" />
                    Phổ biến
                  </div>
                </div>
              )}
              <h3 className="text-xl font-semibold text-gray-900">{plan.name}</h3>
              <div className="mt-4 flex items-baseline">
                <span className="text-4xl font-bold tracking-tight text-gray-900">
                  ${plan.price}
                </span>
                <span className="ml-1 text-lg font-medium text-gray-500">
                  /tháng
                </span>
              </div>
              <p className="mt-2 text-sm text-gray-500">{plan.description}</p>

              <ul className="mt-8 space-y-3">
                {plan.features.map((feature, i) => (
                  <li key={i} className="flex items-start">
                    <Check className="h-5 w-5 flex-shrink-0 text-green-500" />
                    <span className="ml-3 text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>

              <div className="mt-8">
                <Button
                  className={`w-full ${
                    plan.popular
                      ? 'bg-blue-600 hover:bg-blue-700'
                      : 'bg-gray-900 hover:bg-gray-800'
                  }`}
                  size="lg"
                >
                  {plan.buttonText}
                </Button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <p className="text-sm text-gray-500">
            Bạn cần giải pháp tùy chỉnh?{' '}
            <a href="#contact" className="font-medium text-blue-600 hover:underline">
              Liên hệ với chúng tôi
            </a>
          </p>
        </div>
      </div>
    </section>
  );
}
