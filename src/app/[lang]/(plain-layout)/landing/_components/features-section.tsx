import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "~/components/ui/card";
import { CheckCircle2, Rocket, Shield, Zap } from "lucide-react";

const features = [
  {
    icon: <Zap className="h-6 w-6 text-blue-500" />,
    title: "Lightning Fast",
    description: "Tốc độ tải trang nhanh chóng, trải nghiệm mượt mà"
  },
  {
    icon: <Shield className="h-6 w-6 text-green-500" />,
    title: "Bảo mật cao",
    description: "Bảo vệ dữ liệu của bạn với công nghệ bảo mật tiên tiến"
  },
  {
    icon: <Rocket className="h-6 w-6 text-purple-500" />,
    title: "Dễ sử dụng",
    description: "<PERSON>iao diện trực quan, dễ dàng làm quen và sử dụng"
  }
];

export function FeaturesSection() {
  return (
    <section id="features" className="bg-white py-24 sm:py-32">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Tính năng nổi bật
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Khám phá những tính năng ưu việt giúp nâng cao hiệu suất công việc
          </p>
        </div>
        <div className="mx-auto mt-16 max-w-5xl">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {features.map((feature, index) => (
              <Card key={index} className="flex flex-col items-center text-center">
                <CardHeader>
                  <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-50">
                    {feature.icon}
                  </div>
                </CardHeader>
                <CardContent>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                  <CardDescription className="mt-2">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
