'use client';

import Link from 'next/link';
import { Facebook, Twitter, Instagram, Linkedin, Github } from 'lucide-react';

const navigation = {
  product: [
    { name: '<PERSON><PERSON><PERSON> năng', href: '#features' },
    { name: '<PERSON><PERSON>ng giá', href: '#pricing' },
    { name: '<PERSON><PERSON><PERSON> liệu', href: '/docs' },
    { name: 'API', href: '/docs/api' },
  ],
  company: [
    { name: '<PERSON><PERSON> chúng tôi', href: '/about' },
    { name: 'Blog', href: '/blog' },
    { name: '<PERSON><PERSON><PERSON><PERSON> dụng', href: '/careers' },
    { name: '<PERSON><PERSON><PERSON> h<PERSON>', href: '/contact' },
  ],
  legal: [
    { name: '<PERSON><PERSON><PERSON><PERSON> kho<PERSON>n dịch vụ', href: '/terms' },
    { name: '<PERSON><PERSON><PERSON> s<PERSON>ch b<PERSON>o mật', href: '/privacy' },
    { name: '<PERSON><PERSON>', href: '/cookies' },
  ],
  social: [
    {
      name: 'Facebook',
      href: '#',
      icon: Facebook,
    },
    {
      name: 'Instagram',
      href: '#',
      icon: Instagram,
    },
    {
      name: 'Twitter',
      href: '#',
      icon: Twitter,
    },
    {
      name: 'GitHub',
      href: '#',
      icon: Github,
    },
    {
      name: 'LinkedIn',
      href: '#',
      icon: Linkedin,
    },
  ],
};

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900" aria-labelledby="footer-heading">
      <h2 id="footer-heading" className="sr-only">
        Footer
      </h2>
      <div className="mx-auto max-w-7xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-32">
        <div className="xl:grid xl:grid-cols-3 xl:gap-8">
          <div className="space-y-8">
            <div className="flex items-center space-x-2">
              <span className="text-2xl font-bold text-white">YourLogo</span>
            </div>
            <p className="text-sm leading-6 text-gray-300">
              Giải pháp quản lý doanh nghiệp toàn diện giúp bạn tập trung vào điều quan trọng nhất - phát triển kinh doanh.
            </p>
            <div className="flex space-x-6">
              {navigation.social.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-gray-400 hover:text-gray-300"
                >
                  <span className="sr-only">{item.name}</span>
                  <item.icon className="h-6 w-6" aria-hidden="true" />
                </Link>
              ))}
            </div>
          </div>
          <div className="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0">
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold leading-6 text-white">Sản phẩm</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.product.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="text-sm leading-6 text-gray-300 hover:text-white"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="mt-10 md:mt-0">
                <h3 className="text-sm font-semibold leading-6 text-white">Công ty</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.company.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="text-sm leading-6 text-gray-300 hover:text-white"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold leading-6 text-white">Pháp lý</h3>
                <ul role="list" className="mt-6 space-y-4">
                  {navigation.legal.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className="text-sm leading-6 text-gray-300 hover:text-white"
                      >
                        {item.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="mt-10 md:mt-0">
                <h3 className="text-sm font-semibold leading-6 text-white">Liên hệ</h3>
                <ul role="list" className="mt-6 space-y-4">
                  <li>
                    <a
                      href="mailto:<EMAIL>"
                      className="text-sm leading-6 text-gray-300 hover:text-white"
                    >
                      <EMAIL>
                    </a>
                  </li>
                  <li>
                    <a
                      href="tel:+1234567890"
                      className="text-sm leading-6 text-gray-300 hover:text-white"
                    >
                      +1 (234) 567-890
                    </a>
                  </li>
                  <li className="text-sm leading-6 text-gray-300">
                    123 Đường ABC, Quận 1, TP.HCM
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-16 border-t border-white/10 pt-8 sm:mt-20 lg:mt-24">
          <p className="text-xs leading-5 text-gray-400">
            &copy; {currentYear} Your Company, Inc. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
