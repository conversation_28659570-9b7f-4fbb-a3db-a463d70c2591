'use client';

import { Avatar, AvatarFallback, AvatarImage } from '~/components/ui/avatar';

const testimonials = [
  {
    name: '<PERSON><PERSON><PERSON><PERSON>ăn <PERSON>',
    role: 'Gi<PERSON>m đốc điều hành',
    company: 'Công ty ABC',
    content: 'Nền tảng này đã giúp chúng tôi tăng năng suất làm việc lên 200%. Đội ngũ hỗ trợ rất chuyên nghiệp và nhiệt tình.',
    avatar: '/images/avatars/01.png',
  },
  {
    name: 'Trần Thị B',
    role: 'Trưởng phòng Marketing',
    company: 'Công ty XYZ',
    content: 'Giao diện dễ sử dụng, tính năng phong phú. Đặc biệt là khả năng tùy biến cao, phù hợp với nhu cầu của chúng tôi.',
    avatar: '/images/avatars/02.png',
  },
  {
    name: '<PERSON><PERSON>',
    role: 'Founder',
    company: 'Startup DEF',
    content: '<PERSON><PERSON><PERSON><PERSON> pháp hoàn hảo cho các startup như chúng tôi. Chi phí hợp lý nhưng mang lại giá trị vượt trội.',
    avatar: '/images/avatars/03.png',
  },
];

export function Testimonials() {
  return (
    <section className="py-24 bg-white">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Khách hàng nói gì về chúng tôi
          </h2>
          <p className="mt-4 text-lg text-gray-600">
            Hàng ngàn doanh nghiệp đã tin tưởng và đồng hành cùng chúng tôi
          </p>
        </div>

        <div className="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="rounded-xl border bg-card p-6 shadow-sm transition-all hover:shadow-md"
            >
              <div className="flex items-center gap-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                  <AvatarFallback>{testimonial.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="text-left">
                  <h4 className="font-semibold">{testimonial.name}</h4>
                  <p className="text-sm text-muted-foreground">
                    {testimonial.role}, {testimonial.company}
                  </p>
                </div>
              </div>
              <p className="mt-4 text-gray-600">"{testimonial.content}"</p>
              <div className="mt-4 flex text-yellow-400">
                {[...Array(5)].map((_, i) => (
                  <svg
                    key={i}
                    className="h-5 w-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
