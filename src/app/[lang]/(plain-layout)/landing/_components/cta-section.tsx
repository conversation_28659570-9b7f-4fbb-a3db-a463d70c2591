import { Button } from "~/components/ui/button";
import Link from "next/link";

export function CtaSection() {
  return (
    <section className="bg-blue-700">
      <div className="container mx-auto px-4 py-16 sm:px-6 lg:flex lg:items-center lg:justify-between lg:px-8 lg:py-24">
        <div>
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
            Sẵn sàng bắt đầu?
          </h2>
          <p className="mt-4 max-w-3xl text-lg leading-6 text-blue-100">
            Đăng ký ngay hôm nay để trải nghiệm miễn phí 14 ngày. Không cần thẻ tín dụng.
          </p>
        </div>
        <div className="mt-8 flex gap-4 lg:mt-0 lg:flex-shrink-0">
          <Button size="lg" variant="outline" className="bg-transparent text-white hover:bg-blue-600 hover:text-white">
            <Link href="/contact">
              <PERSON><PERSON><PERSON> hệ
            </Link>
          </Button>
          <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50">
            <Link href="/auth/register">
              Đăng ký ngay
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
