import type { Metadata } from "next";
import type { LocaleType } from "~/types";

import { getDictionary } from "~/lib/get-dictionary";
import { SignIn } from "./_components/sign-in";

// Define metadata for the page
// More info: https://nextjs.org/docs/app/building-your-application/optimizing/metadata
export const metadata: Metadata = {
  title: "Sign In",
};

export default async function SignInPage(props: {
  params: Promise<{ lang: LocaleType }>;
}) {
  const params = await props.params;
  const dictionary = await getDictionary(params.lang);

  return <SignIn dictionary={dictionary} />;
}
