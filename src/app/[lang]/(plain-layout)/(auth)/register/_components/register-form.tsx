"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";

import type { LocaleType, RegisterFormType } from "~/types";

import { RegisterSchema } from "~/schemas/register-schema";

import { ensureLocalizedPathname } from "~/lib/i18n";
import { ensureRedirectPathname } from "~/lib/utils";

import { OAuthLinks } from "~/app/[lang]/(plain-layout)/(auth)/_components/oauth-links";
import { ButtonLoading } from "~/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { SeparatorWithText } from "~/components/ui/separator";
import { toast } from "~/hooks/use-toast";

export function RegisterForm() {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();

  const form = useForm<RegisterFormType>({
    resolver: zodResolver(RegisterSchema),
  });

  const locale = params.lang as LocaleType;
  const redirectPathname = searchParams.get("redirectTo");
  const { isSubmitting, isDirty } = form.formState;
  const isDisabled = isSubmitting || !isDirty; // Disable button if form is unchanged or submitting

  async function onSubmit(data: RegisterFormType) {
    const { firstName, lastName, username, email, password } = data;

    try {
      const res = await fetch("/api/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          firstName,
          lastName,
          username,
          email,
          password,
        }),
      });

      if (res && res.status >= 400) {
        const {
          issues,
          message,
        }: {
          issues?: { path: (keyof RegisterFormType)[]; message: string }[];
          message?: string;
        } = await res.json();

        if (!issues) throw new Error(message ?? "An unknown error occurred.");

        // Set errors in React Hook Form based on server response
        issues.forEach(issue => {
          const field = issue.path[0];
          form.setError(field as keyof RegisterFormType, {
            type: "manual",
            message: issue.message,
          });
        });
      } else {
        toast({ title: "Register Successful" });
        router.push(
          ensureLocalizedPathname(
            // Include redirect pathname if available, otherwise default to "/sign-in"
            redirectPathname
              ? ensureRedirectPathname("/sign-in", redirectPathname)
              : "/sign-in",
            locale
          )
        );
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Register Failed",
        description: error instanceof Error ? error.message : undefined,
      });
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-6">
        <div className="grid gap-2">
          <div className="grid grid-cols-2 gap-2">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input type="text" placeholder="John" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input type="text" placeholder="Doe" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Username</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="john_doe" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <Input type="password" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <ButtonLoading isLoading={isSubmitting} disabled={isDisabled}>
          Sign In with Email
        </ButtonLoading>
        <div className="-mt-4 text-center text-sm">
          Already have an account?{" "}
          <Link
            href={ensureLocalizedPathname(
              // Include redirect pathname if available, otherwise default to "/sign-in"
              redirectPathname
                ? ensureRedirectPathname("/sign-in", redirectPathname)
                : "/sign-in",
              locale
            )}
            className="underline"
          >
            Sign in
          </Link>
        </div>
        <SeparatorWithText>Or continue with</SeparatorWithText>
        <OAuthLinks />
      </form>
    </Form>
  );
}
