import { salesRepresentativeData } from "../_data/top-sales-representatives";

import {
  DashboardCard,
  DashboardCardActionsDropdown,
} from "~/app/[lang]/(dashboard-layout)/dashboards/_components/dashboard-card";
import { TopSalesRepresentativesList } from "./top-sales-representatives-list";

export function TopSalesRepresentatives() {
  return (
    <DashboardCard
      title="Top Sales Representatives"
      period={salesRepresentativeData.period}
      action={<DashboardCardActionsDropdown />}
    >
      <TopSalesRepresentativesList
        data={salesRepresentativeData.representatives}
      />
    </DashboardCard>
  );
}
