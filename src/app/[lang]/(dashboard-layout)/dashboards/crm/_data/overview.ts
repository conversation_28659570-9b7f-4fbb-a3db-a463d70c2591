import type { OverviewType } from "../types"

export const overviewData: OverviewType = {
  totalSales: {
    period: "Last month",
    value: 1234567,
    percentageChange: 5.9,
  },
  totalProfit: {
    period: "Last month",
    value: 345678,
    percentageChange: -0.2,
  },
  revenueGrowth: {
    period: "Last month",
    value: 789123,
    percentageChange: 7.3,
  },
  newCustomers: {
    period: "Last month",
    value: 456,
    percentageChange: 12.4,
  },
}
