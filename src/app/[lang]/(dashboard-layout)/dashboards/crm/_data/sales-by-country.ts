import type { SalesByCountryType } from "../types"

export const salesByCountryData: SalesByCountryType = {
  period: "Last month",
  countries: [
    { countryName: "United States", countryCode: "US", sales: 8450 },
    { countryName: "Brazil", countryCode: "BR", sales: 7780 },
    { countryName: "India", countryCode: "IN", sales: 6480 },
    { countryName: "Australia", countryCode: "AU", sales: 5120 },
    { countryName: "France", countryCode: "FR", sales: 4450 },
    { countryName: "Germany", countryCode: "DE", sales: 4320 },
    { countryName: "Canada", countryCode: "CA", sales: 3980 },
    { countryName: "United Kingdom", countryCode: "GB", sales: 3640 },
    { countryName: "Japan", countryCode: "JP", sales: 3280 },
    { countryName: "South Korea", countryCode: "KR", sales: 2920 },
  ],
}
