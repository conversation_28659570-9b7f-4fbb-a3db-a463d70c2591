import type { SalesRepresentativeType } from "../types"

export const salesRepresentativeData: SalesRepresentativeType = {
  period: "Last month",
  representatives: [
    {
      name: "<PERSON>",
      avatar: "/images/avatars/male-01.svg",
      sales: 51099,
      email: "<EMAIL>",
    },
    {
      name: "<PERSON>",
      avatar: "/images/avatars/female-01.svg",
      sales: 45078,
      email: "<EMAIL>",
    },
    {
      name: "<PERSON>",
      avatar: "/images/avatars/male-02.svg",
      sales: 40008,
      email: "<EMAIL>",
    },
    {
      name: "<PERSON>",
      avatar: "/images/avatars/female-03.svg",
      sales: 39000,
      email: "<EMAIL>",
    },
    {
      name: "<PERSON>",
      avatar: "/images/avatars/female-02.svg",
      sales: 22055,
      email: "<EMAIL>",
    },
  ],
}
