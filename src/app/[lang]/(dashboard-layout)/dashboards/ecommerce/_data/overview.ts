import type { OverviewType } from "../types"

export const overviewData: OverviewType = {
  totalSales: {
    value: 1243000,
    percentageChange: 0.08,
    period: "Last month",
  },
  revenueSummary: {
    value: 952000,
    percentageChange: 0.05,
    period: "Last month",
  },
  numberOfOrders: {
    value: 256,
    percentageChange: 0.12,
    period: "Last month",
  },
  averageOrderValue: {
    value: 4648,
    percentageChange: -0.03,
    period: "Last month",
  },
}
