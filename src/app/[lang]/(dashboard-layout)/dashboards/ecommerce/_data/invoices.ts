import type { InvoiceType } from "../types"

export const deliveryStatusesData = [
  { label: "In Transit", value: "In Transit" },
  { label: "Delivered", value: "Delivered" },
  { label: "Processing", value: "Processing" },
  { label: "Pending", value: "Pending" },
]

export const invoicesData: InvoiceType[] = [
  {
    invoiceId: "INV-1001",
    customerName: "John Doe",
    orderDate: "2024-11-01T00:00:00Z",
    dueDate: "2024-11-15T00:00:00Z",
    totalAmount: 250.0,
    deliveryStatus: "Delivered",
  },
  {
    invoiceId: "INV-1002",
    customerName: "<PERSON>",
    orderDate: "2024-11-05T00:00:00Z",
    dueDate: "2024-11-20T00:00:00Z",
    totalAmount: 180.0,
    deliveryStatus: "Shipped",
  },
  {
    invoiceId: "INV-1003",
    customerName: "<PERSON>",
    orderDate: "2024-11-08T00:00:00Z",
    dueDate: "2024-11-22T00:00:00Z",
    totalAmount: 320.0,
    deliveryStatus: "Pending",
  },
  {
    invoiceId: "INV-1004",
    customerName: "<PERSON> <PERSON>",
    orderDate: "2024-11-10T00:00:00Z",
    dueDate: "2024-11-24T00:00:00Z",
    totalAmount: 420.0,
    deliveryStatus: "In Transit",
  },
  {
    invoiceId: "INV-1005",
    customerName: "Michael Tan",
    orderDate: "2024-11-15T00:00:00Z",
    dueDate: "2024-11-30T00:00:00Z",
    totalAmount: 150.0,
    deliveryStatus: "Processing",
  },
  {
    invoiceId: "INV-1006",
    customerName: "Sophia White",
    orderDate: "2024-11-02T00:00:00Z",
    dueDate: "2024-11-16T00:00:00Z",
    totalAmount: 275.0,
    deliveryStatus: "Delivered",
  },
  {
    invoiceId: "INV-1007",
    customerName: "James Wilson",
    orderDate: "2024-11-06T00:00:00Z",
    dueDate: "2024-11-20T00:00:00Z",
    totalAmount: 150.0,
    deliveryStatus: "Shipped",
  },
  {
    invoiceId: "INV-1008",
    customerName: "Olivia Taylor",
    orderDate: "2024-11-07T00:00:00Z",
    dueDate: "2024-11-21T00:00:00Z",
    totalAmount: 310.0,
    deliveryStatus: "Pending",
  },
  {
    invoiceId: "INV-1009",
    customerName: "Liam Harris",
    orderDate: "2024-11-09T00:00:00Z",
    dueDate: "2024-11-23T00:00:00Z",
    totalAmount: 200.0,
    deliveryStatus: "In Transit",
  },
  {
    invoiceId: "INV-1010",
    customerName: "Amelia Clark",
    orderDate: "2024-11-11T00:00:00Z",
    dueDate: "2024-11-25T00:00:00Z",
    totalAmount: 450.0,
    deliveryStatus: "Processing",
  },
  {
    invoiceId: "INV-1011",
    customerName: "Benjamin Allen",
    orderDate: "2024-11-12T00:00:00Z",
    dueDate: "2024-11-26T00:00:00Z",
    totalAmount: 500.0,
    deliveryStatus: "Delivered",
  },
  {
    invoiceId: "INV-1012",
    customerName: "Charlotte King",
    orderDate: "2024-11-13T00:00:00Z",
    dueDate: "2024-11-27T00:00:00Z",
    totalAmount: 370.0,
    deliveryStatus: "Shipped",
  },
  {
    invoiceId: "INV-1013",
    customerName: "Henry Wright",
    orderDate: "2024-11-14T00:00:00Z",
    dueDate: "2024-11-28T00:00:00Z",
    totalAmount: 190.0,
    deliveryStatus: "Pending",
  },
  {
    invoiceId: "INV-1014",
    customerName: "Mia Scott",
    orderDate: "2024-11-15T00:00:00Z",
    dueDate: "2024-11-29T00:00:00Z",
    totalAmount: 230.0,
    deliveryStatus: "In Transit",
  },
  {
    invoiceId: "INV-1015",
    customerName: "Jackson Lee",
    orderDate: "2024-11-16T00:00:00Z",
    dueDate: "2024-11-30T00:00:00Z",
    totalAmount: 350.0,
    deliveryStatus: "Processing",
  },
  {
    invoiceId: "INV-1016",
    customerName: "Harper Evans",
    orderDate: "2024-11-17T00:00:00Z",
    dueDate: "2024-12-01T00:00:00Z",
    totalAmount: 280.0,
    deliveryStatus: "Delivered",
  },
  {
    invoiceId: "INV-1017",
    customerName: "Sebastian Mitchell",
    orderDate: "2024-11-18T00:00:00Z",
    dueDate: "2024-12-02T00:00:00Z",
    totalAmount: 390.0,
    deliveryStatus: "Shipped",
  },
  {
    invoiceId: "INV-1018",
    customerName: "Isabella Garcia",
    orderDate: "2024-11-19T00:00:00Z",
    dueDate: "2024-12-03T00:00:00Z",
    totalAmount: 420.0,
    deliveryStatus: "Pending",
  },
  {
    invoiceId: "INV-1019",
    customerName: "Matthew Lopez",
    orderDate: "2024-11-20T00:00:00Z",
    dueDate: "2024-12-04T00:00:00Z",
    totalAmount: 160.0,
    deliveryStatus: "In Transit",
  },
  {
    invoiceId: "INV-1020",
    customerName: "Avery Rodriguez",
    orderDate: "2024-11-21T00:00:00Z",
    dueDate: "2024-12-05T00:00:00Z",
    totalAmount: 250.0,
    deliveryStatus: "Processing",
  },
  {
    invoiceId: "INV-1021",
    customerName: "David Perez",
    orderDate: "2024-11-22T00:00:00Z",
    dueDate: "2024-12-06T00:00:00Z",
    totalAmount: 300.0,
    deliveryStatus: "Delivered",
  },
  {
    invoiceId: "INV-1022",
    customerName: "Ella Gonzalez",
    orderDate: "2024-11-23T00:00:00Z",
    dueDate: "2024-12-07T00:00:00Z",
    totalAmount: 220.0,
    deliveryStatus: "Shipped",
  },
  {
    invoiceId: "INV-1023",
    customerName: "Lucas Martinez",
    orderDate: "2024-11-24T00:00:00Z",
    dueDate: "2024-12-08T00:00:00Z",
    totalAmount: 280.0,
    deliveryStatus: "Pending",
  },
  {
    invoiceId: "INV-1024",
    customerName: "Zoe Young",
    orderDate: "2024-11-25T00:00:00Z",
    dueDate: "2024-12-09T00:00:00Z",
    totalAmount: 310.0,
    deliveryStatus: "In Transit",
  },
  {
    invoiceId: "INV-1025",
    customerName: "Elijah Davis",
    orderDate: "2024-11-26T00:00:00Z",
    dueDate: "2024-12-10T00:00:00Z",
    totalAmount: 360.0,
    deliveryStatus: "Processing",
  },
  {
    invoiceId: "INV-1026",
    customerName: "Chloe Moore",
    orderDate: "2024-11-27T00:00:00Z",
    dueDate: "2024-12-11T00:00:00Z",
    totalAmount: 180.0,
    deliveryStatus: "Delivered",
  },
  {
    invoiceId: "INV-1027",
    customerName: "Matthew Harris",
    orderDate: "2024-11-28T00:00:00Z",
    dueDate: "2024-12-12T00:00:00Z",
    totalAmount: 250.0,
    deliveryStatus: "Shipped",
  },
  {
    invoiceId: "INV-1028",
    customerName: "Victoria Nelson",
    orderDate: "2024-11-29T00:00:00Z",
    dueDate: "2024-12-13T00:00:00Z",
    totalAmount: 330.0,
    deliveryStatus: "Pending",
  },
  {
    invoiceId: "INV-1029",
    customerName: "Ethan Walker",
    orderDate: "2024-11-30T00:00:00Z",
    dueDate: "2024-12-14T00:00:00Z",
    totalAmount: 220.0,
    deliveryStatus: "In Transit",
  },
  {
    invoiceId: "INV-1030",
    customerName: "Diana Thomas",
    orderDate: "2024-12-01T00:00:00Z",
    dueDate: "2024-12-15T00:00:00Z",
    totalAmount: 250.0,
    deliveryStatus: "Processing",
  },
  {
    invoiceId: "INV-1031",
    customerName: "Lucas Robinson",
    orderDate: "2024-12-02T00:00:00Z",
    dueDate: "2024-12-16T00:00:00Z",
    totalAmount: 360.0,
    deliveryStatus: "Delivered",
  },
  {
    invoiceId: "INV-1032",
    customerName: "Lily Hall",
    orderDate: "2024-12-03T00:00:00Z",
    dueDate: "2024-12-17T00:00:00Z",
    totalAmount: 390.0,
    deliveryStatus: "Shipped",
  },
  {
    invoiceId: "INV-1033",
    customerName: "Sebastian Green",
    orderDate: "2024-12-04T00:00:00Z",
    dueDate: "2024-12-18T00:00:00Z",
    totalAmount: 330.0,
    deliveryStatus: "Pending",
  },
  {
    invoiceId: "INV-1034",
    customerName: "Zara Moore",
    orderDate: "2024-12-05T00:00:00Z",
    dueDate: "2024-12-19T00:00:00Z",
    totalAmount: 420.0,
    deliveryStatus: "In Transit",
  },
  {
    invoiceId: "INV-1035",
    customerName: "Abigail Gonzalez",
    orderDate: "2024-12-06T00:00:00Z",
    dueDate: "2024-12-20T00:00:00Z",
    totalAmount: 350.0,
    deliveryStatus: "Processing",
  },
  {
    invoiceId: "INV-1036",
    customerName: "Tyler Martin",
    orderDate: "2024-12-07T00:00:00Z",
    dueDate: "2024-12-21T00:00:00Z",
    totalAmount: 500.0,
    deliveryStatus: "Delivered",
  },
  {
    invoiceId: "INV-1037",
    customerName: "Sophie Clark",
    orderDate: "2024-12-08T00:00:00Z",
    dueDate: "2024-12-22T00:00:00Z",
    totalAmount: 250.0,
    deliveryStatus: "Shipped",
  },
  {
    invoiceId: "INV-1038",
    customerName: "Jack Mitchell",
    orderDate: "2024-12-09T00:00:00Z",
    dueDate: "2024-12-23T00:00:00Z",
    totalAmount: 400.0,
    deliveryStatus: "Pending",
  },
  {
    invoiceId: "INV-1039",
    customerName: "Samantha Lee",
    orderDate: "2024-12-10T00:00:00Z",
    dueDate: "2024-12-24T00:00:00Z",
    totalAmount: 180.0,
    deliveryStatus: "In Transit",
  },
  {
    invoiceId: "INV-1040",
    customerName: "Eliana Wilson",
    orderDate: "2024-12-11T00:00:00Z",
    dueDate: "2024-12-25T00:00:00Z",
    totalAmount: 370.0,
    deliveryStatus: "Processing",
  },
  {
    invoiceId: "INV-1041",
    customerName: "Henry Perez",
    orderDate: "2024-12-12T00:00:00Z",
    dueDate: "2024-12-26T00:00:00Z",
    totalAmount: 450.0,
    deliveryStatus: "Delivered",
  },
  {
    invoiceId: "INV-1042",
    customerName: "Isaac Baker",
    orderDate: "2024-12-13T00:00:00Z",
    dueDate: "2024-12-27T00:00:00Z",
    totalAmount: 390.0,
    deliveryStatus: "Shipped",
  },
  {
    invoiceId: "INV-1043",
    customerName: "Dylan Brooks",
    orderDate: "2024-12-14T00:00:00Z",
    dueDate: "2024-12-28T00:00:00Z",
    totalAmount: 220.0,
    deliveryStatus: "Pending",
  },
  {
    invoiceId: "INV-1044",
    customerName: "Ella Carter",
    orderDate: "2024-12-15T00:00:00Z",
    dueDate: "2024-12-29T00:00:00Z",
    totalAmount: 330.0,
    deliveryStatus: "In Transit",
  },
  {
    invoiceId: "INV-1045",
    customerName: "Sophie Green",
    orderDate: "2024-12-16T00:00:00Z",
    dueDate: "2024-12-30T00:00:00Z",
    totalAmount: 250.0,
    deliveryStatus: "Processing",
  },
]
