"use client";

import { <PERSON>, Area<PERSON>hart, CartesianGrid, XAxis } from "recharts";

import type { ConversionFunnelType } from "../types";

import { ChartContainer } from "~/components/ui/chart";
import { useIsRtl } from "~/hooks/use-is-rtl";

export function ConversionFunnelChart({
  data,
}: {
  data: ConversionFunnelType["funnelSteps"];
}) {
  const isRtl = useIsRtl();

  return (
    <ChartContainer config={{}} className="aspect-video h-40 w-full">
      <AreaChart
        accessibilityLayer
        data={data}
        margin={{
          left: 0,
          right: 0,
        }}
      >
        <CartesianGrid vertical={false} />
        <XAxis reversed={isRtl} dataKey="name" hide />
        <Area
          dataKey="value"
          type="bump"
          activeDot={false}
          fill="var(--chart-2)"
          fillOpacity={0.4}
          stroke="var(--chart-2)"
        />
      </AreaChart>
    </ChartContainer>
  );
}
