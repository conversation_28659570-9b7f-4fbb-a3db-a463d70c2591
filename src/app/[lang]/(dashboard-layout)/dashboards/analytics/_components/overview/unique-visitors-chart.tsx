"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, XAxis } from "recharts";

import type { ChartConfig } from "~/components/ui/chart";
import type { OverviewType } from "../../types";

import {
  Chart<PERSON>ontainer,
  ChartTooltip,
  ChartTooltipContent,
} from "~/components/ui/chart";
import { useIsRtl } from "~/hooks/use-is-rtl";

const chartConfig = {
  value: {
    label: "Visitors",
  },
} satisfies ChartConfig;

export function UniqueVisitorsChart({
  data,
}: {
  data: OverviewType["uniqueVisitors"]["perMonth"];
}) {
  const isRtl = useIsRtl();

  return (
    <ChartContainer
      config={chartConfig}
      className="h-32 w-full rounded-b-md overflow-hidden"
    >
      <AreaChart
        accessibilityLayer
        data={data}
        margin={{
          left: 0,
          right: 0,
        }}
      >
        <CartesianGrid vertical={false} />
        <XAxis
          reversed={isRtl}
          dataKey="month"
          tickLine={false}
          axisLine={false}
          tickMargin={8}
          hide
          tickFormatter={value => value.slice(0, 3)}
        />
        <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
        <Area
          dataKey="value"
          type="natural"
          fill="var(--chart-1)"
          fillOpacity={0.4}
          stroke="var(--chart-1)"
        />
      </AreaChart>
    </ChartContainer>
  );
}
