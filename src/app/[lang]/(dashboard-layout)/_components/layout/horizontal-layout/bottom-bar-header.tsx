"use client";

import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";

import type { DictionaryType } from "~/lib/get-dictionary";
import type { LocaleType } from "~/types";

import { ensureLocalizedPathname } from "~/lib/i18n";

import { LanguageDropdown } from "~/components/language-dropdown";
import { FullscreenToggle } from "../full-screen-toggle";
import { ModeDropdown } from "../mode-dropdown";
import { NotificationDropdown } from "../notification-dropdown";
import { ToggleMobileSidebar } from "../toggle-mobile-sidebar";
import { UserDropdown } from "../user-dropdown";

export function BottomBarHeader({
  dictionary,
}: {
  dictionary: DictionaryType;
}) {
  const params = useParams();
  const locale = params.lang as LocaleType;

  return (
    <div className="container flex h-14 justify-between items-center gap-4">
      <ToggleMobileSidebar />
      <Link
        href={ensureLocalizedPathname("/", locale)}
        className="hidden text-foreground font-black lg:flex"
      >
        <Image
          src="/images/icons/logo.svg"
          alt=""
          height={60}
          width={60}
          className="dark:invert"
        />
      </Link>
      <div className="flex gap-2">
        <NotificationDropdown dictionary={dictionary} />
        <FullscreenToggle />
        <ModeDropdown dictionary={dictionary} />
        <LanguageDropdown dictionary={dictionary} />
        <UserDropdown dictionary={dictionary} locale={locale} />
      </div>
    </div>
  );
}
