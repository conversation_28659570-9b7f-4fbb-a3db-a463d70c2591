"use client";

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "~/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";

export function BasicSelect() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Basic Select</CardTitle>
      </CardHeader>
      <CardContent className="flex justify-center items-center">
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Theme" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="light">Light</SelectItem>
            <SelectItem value="dark">Dark</SelectItem>
            <SelectItem value="system">System</SelectItem>
          </SelectContent>
        </Select>
      </CardContent>
    </Card>
  );
}
