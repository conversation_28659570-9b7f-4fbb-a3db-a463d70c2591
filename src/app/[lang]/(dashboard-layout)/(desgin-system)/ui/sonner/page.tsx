import type { Metada<PERSON> } from "next";

import { ActionSonner } from "./_components/action-sonner";
import { DefaultSonner } from "./_components/default-sonner";
import { DismissibleSonner } from "./_components/dismissible-sonner";
import { <PERSON><PERSON><PERSON><PERSON>onner } from "./_components/error-sonner";
import { InfoSonner } from "./_components/info-sonner";
import { PromiseSonner } from "./_components/promise-sonner";
import { SuccessSonner } from "./_components/success-sonner";
import { WarningSonner } from "./_components/warning-sonner";

// Define metadata for the page
// More info: https://nextjs.org/docs/app/building-your-application/optimizing/metadata
export const metadata: Metadata = {
  title: "Sonner",
};

export default function SonnerPage() {
  return (
    <section className="container grid gap-4 p-4 md:grid-cols-2 lg:grid-cols-3">
      <DefaultSonner />
      <ActionSonner />
      <DismissibleSonner />
      <SuccessSonner />
      <ErrorSonner />
      <WarningSonner />
      <InfoSonner />
      <PromiseSonner />
    </section>
  );
}
