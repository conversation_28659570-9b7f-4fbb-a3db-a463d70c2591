"use client";

import { But<PERSON> } from "~/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { toast } from "~/hooks/use-toast";

export function DefaultSonner() {
  const showBasicToast = () => {
    toast({
      title: "Default Notification",
      description: "This is a basic notification",
      action: {
        label: "Undo",
        onClick: () => console.log("Undone"),
      },
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Default Toast</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-2">
        <Button variant="outline" onClick={showBasicToast}>
          Show Default Toast
        </Button>
      </CardContent>
    </Card>
  );
}
