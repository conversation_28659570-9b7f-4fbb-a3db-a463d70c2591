"use client";

import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { toast } from "~/hooks/use-toast";

export function WarningSonner() {
  const showWarningToast = () => {
    toast({
      variant: "warning",
      title: "Warning",
      description: "Please check the information before continuing",
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Warning Toast</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-2">
        <Button variant="outline" onClick={showWarningToast}>
          Show Warning Toast
        </Button>
      </CardContent>
    </Card>
  );
}
