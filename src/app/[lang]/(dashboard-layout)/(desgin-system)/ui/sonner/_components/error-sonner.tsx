"use client";

import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { toast } from "~/hooks/use-toast";

export function ErrorSonner() {
  const showErrorToast = () => {
    toast({
      variant: "destructive",
      title: "Error",
      description: "An error occurred while performing the operation",
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Error Toast</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-2">
        <Button variant="outline" onClick={showErrorToast}>
          Show Error Toast
        </Button>
      </CardContent>
    </Card>
  );
}
