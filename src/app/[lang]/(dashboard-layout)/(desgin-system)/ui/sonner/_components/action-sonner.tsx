"use client";

import { But<PERSON> } from "~/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { toast } from "~/hooks/use-toast";

export function ActionSonner() {
  const showActionToast = () => {
    toast({
      title: "Action Notification",
      description: "You can perform an action from this notification",
      action: {
        label: "View Details",
        onClick: () => console.log("Clicked on view details"),
      },
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Action Toast</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-2">
        <Button variant="outline" onClick={showActionToast}>
          Show Action Toast
        </Button>
      </CardContent>
    </Card>
  );
}
