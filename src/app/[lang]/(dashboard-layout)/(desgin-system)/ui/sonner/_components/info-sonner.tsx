"use client";

import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { toast } from "~/hooks/use-toast";

export function InfoSonner() {
  const showInfoToast = () => {
    toast({
      variant: "info",
      title: "Information",
      description: "This is an informational notification",
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Info Toast</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-2">
        <Button variant="outline" onClick={showInfoToast}>
          Show Info Toast
        </Button>
      </CardContent>
    </Card>
  );
}
