"use client";

import { toast as sonnerToast } from "sonner";

import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";

export function PromiseSonner() {
  const showPromiseToast = () => {
    // Using sonnerToast.promise directly because use-toast.ts doesn't support promises yet
    sonnerToast.promise(
      new Promise<{ name: string }>((resolve) => {
        setTimeout(() => resolve({ name: "Promise" }), 2000);
      }),
      {
        loading: "Loading...",
        success: (data) => `${data.name} completed successfully`,
        error: "An error occurred",
      }
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Promise Toast</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-2">
        <Button variant="outline" onClick={showPromiseToast}>
          Show Promise Toast
        </Button>
      </CardContent>
    </Card>
  );
}
