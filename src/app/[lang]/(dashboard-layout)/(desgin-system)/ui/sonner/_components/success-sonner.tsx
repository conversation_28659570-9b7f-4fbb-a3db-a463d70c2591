"use client";

import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { toast } from "~/hooks/use-toast";

export function SuccessSonner() {
  const showSuccessToast = () => {
    toast({
      variant: "success",
      title: "Success",
      description: "Operation completed successfully",
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Success Toast</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-2">
        <Button variant="outline" onClick={showSuccessToast}>
          Show Success Toast
        </Button>
      </CardContent>
    </Card>
  );
}
