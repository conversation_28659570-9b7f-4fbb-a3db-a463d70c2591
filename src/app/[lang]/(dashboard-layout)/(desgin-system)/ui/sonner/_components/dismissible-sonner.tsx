"use client";

import { But<PERSON> } from "~/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { toast } from "~/hooks/use-toast";

export function DismissibleSonner() {
  const showDismissibleToast = () => {
    const { dismiss } = toast({
      title: "Dismissible Notification",
      description: "Click the button to dismiss this notification",
      duration: 10000, // 10 seconds
    });

    // Create another toast with a button to dismiss the first toast
    toast({
      title: "Dismiss the notification above",
      action: {
        label: "Dismiss",
        onClick: () => dismiss(),
      },
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Dismissible Toast</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-2">
        <Button variant="outline" onClick={showDismissibleToast}>
          Show Dismissible Toast
        </Button>
      </CardContent>
    </Card>
  );
}
