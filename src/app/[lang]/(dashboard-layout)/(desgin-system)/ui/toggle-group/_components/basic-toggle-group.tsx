"use client";

import { Bold, Italic, Underline } from "lucide-react";

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "~/components/ui/card";
import { ToggleGroup, ToggleGroupItem } from "~/components/ui/toggle-group";

export function BasicToggleGroup() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Basic Toggle Group</CardTitle>
      </CardHeader>
      <CardContent className="flex justify-center items-center">
        <ToggleGroup type="multiple">
          <ToggleGroupItem value="bold" aria-label="Toggle bold">
            <Bold className="h-4 w-4" />
          </ToggleGroupItem>
          <ToggleGroupItem value="italic" aria-label="Toggle italic">
            <Italic className="h-4 w-4" />
          </ToggleGroupItem>
          <ToggleGroupItem
            value="strikethrough"
            aria-label="Toggle strikethrough"
          >
            <Underline className="h-4 w-4" />
          </ToggleGroupItem>
        </ToggleGroup>
      </CardContent>
    </Card>
  );
}
