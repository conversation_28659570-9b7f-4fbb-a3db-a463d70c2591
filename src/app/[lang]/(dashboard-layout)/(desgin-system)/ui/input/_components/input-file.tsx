"use client";

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";

export function InputFile() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Input File</CardTitle>
      </CardHeader>
      <CardContent className="flex justify-center items-center">
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="picture">Picture</Label>
          <Input id="picture" type="file" />
        </div>
      </CardContent>
    </Card>
  );
}
