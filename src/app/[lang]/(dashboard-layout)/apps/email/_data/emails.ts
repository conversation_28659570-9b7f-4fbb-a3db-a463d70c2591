import type { EmailType } from "../types"

export const emailsData: EmailType[] = [
  {
    id: "1",
    sender: {
      id: "1",
      name: "<PERSON>",
      avatar: "/images/avatars/male-01.svg",
      email: "<EMAIL>",
      status: "Active",
    },
    subject: "How to Succeed with Your Shopify Store",
    content:
      "Hi, <PERSON> here!\n\nI wanted to share some tips and strategies that have helped me scale my Shopify store...",
    read: false,
    starred: false,
    createdAt: "2024-11-07T10:46:00Z",
    label: "work",
    isDraft: false,
    isSent: false,
    isStarred: false,
    isSpam: false,
    isDeleted: false,
  },
  {
    id: "2",
    sender: {
      id: "2",
      name: "<PERSON>",
      avatar: "/images/avatars/female-02.svg",
      email: "<EMAIL>",
      status: "Inactive",
    },
    subject: "Please find attached the latest Company Report",
    content:
      "Dear Team,\n\nAttached you will find the most recent company report...",
    read: true,
    starred: true,
    createdAt: "2024-11-07T10:55:00Z",
    label: "important",
    isDraft: false,
    isSent: false,
    isStarred: true,
    isSpam: false,
    isDeleted: false,
  },
  {
    id: "3",
    sender: {
      id: "3",
      name: "<PERSON> <PERSON>",
      avatar: "/images/avatars/female-03.svg",
      email: "<EMAIL>",
      status: "Active",
    },
    subject: "Update Can Change Your Personal Life",
    content:
      "Hi, it's Olivia.\n\nI wanted to share some recent updates that have greatly impacted my personal life...",
    read: true,
    starred: false,
    createdAt: "2024-11-07T12:04:00Z",
    label: "personal",
    isDraft: true,
    isSent: false,
    isStarred: false,
    isSpam: false,
    isDeleted: true,
  },
  {
    id: "4",
    sender: {
      id: "4",
      name: "Michael Brown",
      avatar: "/images/avatars/male-02.svg",
      email: "<EMAIL>",
      status: "Active",
    },
    subject: "Weekly Team Meeting Agenda",
    content:
      "Hey everyone,\n\nI just wanted to remind you all about the weekly team meeting tomorrow...",
    read: false,
    starred: false,
    createdAt: "2024-11-07T14:30:00Z",
    label: "work",
    isDraft: false,
    isSent: false,
    isStarred: false,
    isSpam: true,
    isDeleted: false,
  },
  {
    id: "5",
    sender: {
      id: "5",
      name: "Emily Smith",
      avatar: "/images/avatars/female-01.svg",
      email: "<EMAIL>",
      status: "Inactive",
    },
    subject: "New Product Launch Details",
    content:
      "Hi Team,\n\nExciting news! We are launching a new product next week...",
    read: true,
    starred: true,
    createdAt: "2024-11-07T16:15:00Z",
    label: "important",
    isDraft: false,
    isSent: true,
    isStarred: true,
    isSpam: false,
    isDeleted: false,
  },
  {
    id: "11",
    sender: {
      id: "11",
      name: "Emily Smith",
      avatar: "/images/avatars/female-01.svg",
      email: "<EMAIL>",
      status: "Active",
    },
    subject: "Welcome to the Company!",
    content: `Dear New Employee,

Welcome aboard! We are excited to have you join our team. Please find attached the employee handbook, and don't forget to complete your onboarding paperwork. 

If you have any questions or need help settling in, don’t hesitate to reach out. We look forward to working with you!

Best,  
Emily`,
    read: false,
    starred: true,
    createdAt: "2024-11-08T14:20:00Z",
    label: "work",
    isDraft: false,
    isSent: false,
    isStarred: true,
    isSpam: false,
    isDeleted: false,
  },
  {
    id: "12",
    sender: {
      id: "12",
      name: "Michael Brown",
      avatar: "/images/avatars/male-02.svg",
      email: "<EMAIL>",
      status: "Inactive",
    },
    subject: "Reminder: Subscription Renewal",
    content: `Dear Customer,

This is a reminder that your subscription will be renewing soon. If you wish to cancel or update your plan, please do so before the renewal date. 

If you have any questions or need assistance, our support team is here to help. Thank you for your continued support.

Best regards,  
Michael`,
    read: true,
    starred: false,
    createdAt: "2024-11-08T15:40:00Z",
    isDraft: false,
    isSent: true,
    isStarred: false,
    isSpam: false,
    isDeleted: true,
  },
  {
    id: "13",
    sender: {
      id: "13",
      name: "Olivia Martinez",
      avatar: "/images/avatars/female-03.svg",
      email: "<EMAIL>",
      status: "Active",
    },
    subject: "Invitation to Join Our Webinar",
    content: `Hi there!

We are hosting a free webinar next week, and we would love for you to join. The topic will be 'Mastering Digital Marketing for 2024'. It's a great opportunity to learn valuable strategies from industry experts. 

Click here to register, and we hope to see you there!

Best regards,  
Olivia`,
    read: false,
    starred: true,
    createdAt: "2024-11-08T16:55:00Z",
    label: "important",
    isDraft: false,
    isSent: false,
    isStarred: true,
    isSpam: false,
    isDeleted: false,
  },
  {
    id: "14",
    sender: {
      id: "14",
      name: "John Doe",
      avatar: "/images/avatars/male-01.svg",
      email: "<EMAIL>",
      status: "Inactive",
    },
    subject: "System Maintenance Notification",
    content: `Dear Users,

Please be aware that our system will undergo maintenance this weekend from Friday 10:00 PM to Saturday 2:00 AM. During this time, there may be intermittent outages. 

We apologize for any inconvenience and encourage you to plan accordingly. Thank you for your understanding.

Best regards,  
John`,
    read: true,
    starred: false,
    createdAt: "2024-11-08T17:10:00Z",
    label: "important",
    isDraft: false,
    isSent: false,
    isStarred: false,
    isSpam: false,
    isDeleted: false,
  },
  {
    id: "15",
    sender: {
      id: "15",
      name: "Sarah Johnson",
      avatar: "/images/avatars/female-02.svg",
      email: "<EMAIL>",
      status: "Active",
    },
    subject: "Thank You for Your Purchase!",
    content: `Dear Customer,

Thank you for your recent purchase! We're excited for you to enjoy your new product. If you have any questions about the product or need assistance with setup, our customer service team is here to help. 

Feel free to reach out anytime. Enjoy your purchase!

Best regards,  
Sarah`,
    read: false,
    starred: false,
    createdAt: "2024-11-08T18:25:00Z",
    label: "work",
    isDraft: false,
    isSent: false,
    isStarred: false,
    isSpam: true,
    isDeleted: false,
  },
  {
    id: "21",
    sender: {
      id: "21",
      name: "John Doe",
      avatar: "/images/avatars/male-01.svg",
      email: "<EMAIL>",
      status: "Active",
    },
    subject: "Product Feedback Survey",
    content:
      "Hello,\n\nWe’d love to hear your thoughts on our recent product. Your feedback will help us improve and ensure we’re providing the best possible experience. The survey should only take a few minutes to complete.\n\nThank you in advance for your time!\n\nBest, John",
    read: false,
    starred: false,
    createdAt: "2024-11-09T12:00:00Z",
    label: "personal",
    isDraft: false,
    isSent: false,
    isStarred: false,
    isSpam: false,
    isDeleted: false,
  },
  {
    id: "22",
    sender: {
      id: "22",
      name: "Michael Brown",
      email: "<EMAIL>",
      status: "Inactive",
    },
    subject: "Scheduled Maintenance Alert",
    content:
      "Dear Customer,\n\nWe will be conducting scheduled maintenance on our website this Saturday from 12:00 AM to 6:00 AM. During this time, our services may be temporarily unavailable.\n\nWe apologize for any inconvenience and appreciate your understanding.\n\nBest regards, Michael",
    read: true,
    starred: false,
    createdAt: "2024-11-09T13:00:00Z",
    label: "important",
    isDraft: false,
    isSent: true,
    isStarred: false,
    isSpam: false,
    isDeleted: false,
  },
  {
    id: "23",
    sender: {
      id: "23",
      name: "Olivia Martinez",
      email: "<EMAIL>",
      status: "Active",
    },
    subject: "Action Required: Tax Form Submission",
    content:
      "Hi there,\n\nThis is a reminder to submit your tax forms by the end of this week. If you have not yet submitted them, please complete the necessary paperwork as soon as possible to avoid any penalties.\n\nLet me know if you need assistance.\n\nThanks, Olivia",
    read: true,
    starred: true,
    createdAt: "2024-11-09T13:30:00Z",
    label: "important",
    isDraft: false,
    isSent: false,
    isStarred: true,
    isSpam: false,
    isDeleted: false,
  },
  {
    id: "24",
    sender: {
      id: "24",
      name: "Sarah Johnson",
      email: "<EMAIL>",
      status: "Inactive",
    },
    subject: "Monthly Report Submission Reminder",
    content:
      "Hi team,\n\nJust a quick reminder to submit your monthly reports by end of day Monday. We need all reports in order to prepare for the upcoming board meeting.\n\nPlease let me know if there are any issues with your submissions.\n\nBest, Sarah",
    read: false,
    starred: false,
    createdAt: "2024-11-09T14:00:00Z",
    label: "work",
    isDraft: true,
    isSent: false,
    isStarred: false,
    isSpam: false,
    isDeleted: false,
  },
  {
    id: "25",
    sender: {
      id: "25",
      name: "Emily Smith",
      email: "<EMAIL>",
      status: "Active",
    },
    subject: "Meeting Confirmation for Next Week",
    content:
      "Hello,\n\nI just wanted to confirm the meeting scheduled for next week on Tuesday at 11:00 AM. Please make sure to attend, as we’ll be discussing important updates and upcoming projects.\n\nLet me know if you need to reschedule.\n\nBest regards, Emily",
    read: false,
    starred: true,
    createdAt: "2024-11-09T14:30:00Z",
    label: "work",
    isDraft: false,
    isSent: false,
    isStarred: true,
    isSpam: false,
    isDeleted: false,
  },
  {
    id: "26",
    sender: {
      id: "26",
      name: "Michael Brown",
      email: "<EMAIL>",
      status: "Inactive",
    },
    subject: "Important: Insurance Claim Submission",
    content:
      "Dear customer,\n\nThis is an important reminder to submit your insurance claim by the deadline. If you need assistance with your claim, please reach out to our support team. We’re here to help!\n\nBest regards, Michael",
    read: true,
    starred: false,
    createdAt: "2024-11-09T15:00:00Z",
    label: "important",
    isDraft: false,
    isSent: false,
    isStarred: false,
    isSpam: false,
    isDeleted: false,
  },
  {
    id: "27",
    sender: {
      id: "27",
      name: "Olivia Martinez",
      email: "<EMAIL>",
      status: "Active",
    },
    subject: "Website Design Proposal",
    content:
      "Hello,\n\nI’ve attached the proposal for the website redesign project. Please review the document and provide any feedback before our meeting next Thursday.\n\nI look forward to your thoughts.\n\nBest, Olivia",
    read: false,
    starred: false,
    createdAt: "2024-11-09T15:30:00Z",
    label: "work",
    isDraft: false,
    isSent: true,
    isStarred: false,
    isSpam: false,
    isDeleted: false,
  },
  {
    id: "28",
    sender: {
      id: "28",
      name: "Sarah Johnson",
      email: "<EMAIL>",
      status: "Inactive",
    },
    subject: "Job Application Status Update",
    content:
      "Dear applicant,\n\nThank you for your interest in the position at our company. After careful review, we regret to inform you that we have decided to move forward with another candidate.\n\nWe appreciate your time and wish you the best in your job search.\n\nBest regards, Sarah",
    read: true,
    starred: false,
    createdAt: "2024-11-09T16:00:00Z",
    label: "personal",
    isDraft: false,
    isSent: false,
    isStarred: false,
    isSpam: false,
    isDeleted: true,
  },
  {
    id: "29",
    sender: {
      id: "29",
      name: "Emily Smith",
      email: "<EMAIL>",
      status: "Active",
    },
    subject: "Reminder: Event Registration Deadline",
    content:
      "Hi there,\n\nJust a reminder that the registration deadline for our upcoming event is this Friday. Please make sure to sign up if you plan to attend.\n\nWe look forward to seeing you there!\n\nBest regards, Emily",
    read: false,
    starred: true,
    createdAt: "2024-11-09T16:30:00Z",
    label: "important",
    isDraft: false,
    isSent: false,
    isStarred: true,
    isSpam: false,
    isDeleted: false,
  },
  {
    id: "30",
    sender: {
      id: "30",
      name: "John Doe",
      email: "<EMAIL>",
      status: "Inactive",
    },
    subject: "Holiday Schedule Confirmation",
    content:
      "Hi team,\n\nI just wanted to confirm the holiday schedule for the upcoming month. Please let me know if there are any changes, as I need to finalize the roster.\n\nThanks for your cooperation!\n\nBest regards, John",
    read: true,
    starred: false,
    createdAt: "2024-11-09T17:00:00Z",
    label: "work",
    isDraft: false,
    isSent: true,
    isStarred: false,
    isSpam: false,
    isDeleted: false,
  },
]
