"use client";

import { useDirection } from "@radix-ui/react-direction";
import CodeBlock from "@tiptap/extension-code-block";
import Color from "@tiptap/extension-color";
import Highlight from "@tiptap/extension-highlight";
import Image from "@tiptap/extension-image";
import Link from "@tiptap/extension-link";
import Placeholder from "@tiptap/extension-placeholder";
import Subscript from "@tiptap/extension-subscript";
import Superscript from "@tiptap/extension-superscript";
import Table from "@tiptap/extension-table";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import TableRow from "@tiptap/extension-table-row";
import TaskItem from "@tiptap/extension-task-item";
import TaskList from "@tiptap/extension-task-list";
import TextAlign from "@tiptap/extension-text-align";
import TextStyle from "@tiptap/extension-text-style";
import Typography from "@tiptap/extension-typography";
import Underline from "@tiptap/extension-underline";
import Youtube from "@tiptap/extension-youtube";
import { BubbleMenu, EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";

import type { UseEditorOptions } from "@tiptap/react";

import { cn } from "~/lib/utils";

import { Card } from "~/components/ui/card";
import { ScrollArea } from "~/components/ui/scroll-area";
import { useIsRtl } from "~/hooks/use-is-rtl";
import { EditorMenuBar } from "./editor-menu-bar";

interface EditorProps extends UseEditorOptions {
  value?: string;
  onValueChange?: (value: string) => void;
  bubbleMenu?: boolean;
  placeholder?: string;
  className?: string;
}

export function Editor({
  value,
  onValueChange,
  bubbleMenu = false,
  placeholder,
  className,
  ...props
}: EditorProps) {
  const direction = useDirection();
  const isRtl = useIsRtl();

  const editor = useEditor({
    immediatelyRender: false,
    editorProps: {
      attributes: {
        class: cn(
          "px-3 py-2 break-all [&_p]:m-0 [&_.is-editor-empty]:before:absolute [&_.is-editor-empty]:before:top-2 [&_.is-editor-empty]:before:cursor-text [&_.is-editor-empty]:before:text-muted-foreground [&_.is-editor-empty]:before:content-[attr(data-placeholder)] prose prose-headings:font-normal prose-headings:text-foreground prose-h1:text-2xl prose-h2:text-xl prose-h3:text-lg dark:prose-invert focus:outline-hidden [&_table_th.selectedCell]:bg-blue-100 [&_table_td.selectedCell]:bg-blue-50 dark:[&_table_th.selectedCell]:bg-blue-800/20 dark:[&_table_td.selectedCell]:bg-blue-900/10",
          className
        ),
      },
    },
    extensions: [
      StarterKit.configure({
        codeBlock: false,
      }),
      Underline,
      TextAlign.configure({
        types: ["heading", "paragraph"],
        defaultAlignment: isRtl ? "right" : "left",
      }),
      TextStyle,
      Color,
      Image,
      Link.configure({
        openOnClick: true,
        HTMLAttributes: {
          rel: "noopener noreferrer",
          target: "_blank",
        },
      }),
      Placeholder.configure({
        placeholder: placeholder,
        showOnlyCurrent: true,
      }),
      Typography,
      // Các extension mới
      Highlight.configure({
        multicolor: true,
        HTMLAttributes: {
          style:
            "background-color: #fef08a; padding: 0 0.25rem; border-radius: 0.25rem;",
        },
      }),
      Superscript,
      Subscript,
      CodeBlock,
      TaskList.configure({
        HTMLAttributes: {
          style: "list-style: none; padding: 0;",
        },
      }),
      TaskItem.configure({
        nested: true,
        HTMLAttributes: {
          style:
            "display: flex; align-items: flex-start; gap: 0.5rem; margin: auto 0;",
        },
      }),
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          style:
            "border-collapse: collapse; width: 100%; table-layout: fixed; border: 2px solid #e5e7eb;",
        },
      }),
      TableRow.configure({
        HTMLAttributes: {
          style: "border-bottom: 1px solid #e5e7eb;",
        },
      }),
      TableHeader.configure({
        HTMLAttributes: {
          style:
            "font-weight: 600; text-align: left; padding: 0.5rem; border: 1px solid #d1d5db;",
        },
      }),
      TableCell.configure({
        HTMLAttributes: {
          style:
            "border: 1px solid #e5e7eb; padding: 0.5rem; vertical-align: top;",
        },
      }),
      Youtube.configure({
        width: 640,
        height: 360,
        nocookie: true,
        progressBarColor: "white",
        HTMLAttributes: {
          style:
            "width: 100%; height: auto; max-width: 100%; aspect-ratio: 16/9; margin: 0 auto;",
          class: "youtube-embed",
        },
      }),
    ],
    content: value,
    onUpdate: ({ editor }) => {
      onValueChange?.(editor.getHTML());
    },
    ...props,
  });

  if (!editor) {
    return null;
  }

  return (
    <Card>
      {bubbleMenu ? (
        <BubbleMenu
          className="z-50 h-auto rounded-md border bg-popover text-popover-foreground shadow-md outline-hidden"
          editor={editor}
          tippyOptions={{
            duration: 100,
            maxWidth: "98vw",
            appendTo: document.body,
            zIndex: 50,
          }}
        >
          <EditorMenuBar editor={editor} />
        </BubbleMenu>
      ) : (
        <EditorMenuBar editor={editor} />
      )}
      <ScrollArea
        className={cn(
          "flex flex-col min-h-9 cursor-text",
          !bubbleMenu ? "border-t border-border rounded-b-md" : "rounded-md",
          editor.isFocused && "outline-hidden ring-1 ring-ring"
        )}
      >
        <EditorContent
          editor={editor}
          dir={direction}
          className={cn(
            editor.isActive({ textAlign: "left" }) &&
              "[&_.is-editor-empty]:before:left-3",
            editor.isActive({ textAlign: "right" }) &&
              "[&_.is-editor-empty]:before:right-3",
            editor.isActive({ textAlign: "center" }) &&
              "[&_.is-editor-empty]:before:left-1/2 [&_.is-editor-empty]:before:absolute [&_.is-editor-empty]:before:-translate-x-1/2",
            editor.isActive({ textAlign: "justify" }) &&
              "[&_.is-editor-empty]:before:left-3"
          )}
          onClick={() => editor.commands.focus()}
        />
      </ScrollArea>
    </Card>
  );
}
