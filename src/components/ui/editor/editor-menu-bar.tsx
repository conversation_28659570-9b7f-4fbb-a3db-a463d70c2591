"use client";

import {
  <PERSON><PERSON><PERSON>,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  Check,
  <PERSON>umns,
  Highlighter,
  ImageIcon,
  LinkIcon,
  List,
  ListTodo,
  Minus,
  Palette,
  Rows,
  SquareCode,
  Subscript,
  Superscript,
  Table,
  Type,
  Unlink,
  X,
  Youtube,
} from "lucide-react";
import { useRef, useState } from "react";

import type { ChainedCommands, Editor } from "@tiptap/react";
import type { FormEvent } from "react";
import type { DynamicIconNameType } from "~/types";

import { cn } from "~/lib/utils";

import { DynamicIcon } from "~/components/dynamic-icon";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { InputFile } from "~/components/ui/input-file";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import { Separator } from "~/components/ui/separator";
import { Toggle } from "~/components/ui/toggle";

interface SizeType {
  label: string;
  level: 1 | 2 | 3;
  textSize: `text-${string}`;
}

const sizes: SizeType[] = [
  { label: "Normal", level: 3, textSize: "text-lg" },
  { label: "Large", level: 2, textSize: "text-xl" },
  { label: "Extra Large", level: 1, textSize: "text-2xl" },
];

function SizeHandler({ editor }: { editor: Editor }) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          aria-label="Select text style"
        >
          <Type className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="min-w-[8rem] w-auto p-1">
        <div className="flex flex-col">
          <Button
            variant="ghost"
            onClick={() => editor.chain().focus().setParagraph().run()}
            className={cn(
              "justify-start px-3 py-2 text-left text-base",
              editor.isActive("paragraph") && "bg-muted"
            )}
          >
            Small
          </Button>
          {sizes.map((size: SizeType) => (
            <Button
              key={size.level}
              variant="ghost"
              onClick={() =>
                editor
                  .chain()
                  .focus()
                  .toggleHeading({ level: size.level })
                  .run()
              }
              className={cn(
                "justify-start px-3 py-2 text-left",
                size.textSize,
                editor.isActive("heading", { level: size.level }) && "bg-muted"
              )}
            >
              {size.label}
            </Button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}

interface FormatType {
  format: string;
  iconName: DynamicIconNameType;
}

const formats: FormatType[] = [
  {
    format: "bold",
    iconName: "Bold",
  },
  {
    format: "italic",
    iconName: "Italic",
  },
  {
    format: "underline",
    iconName: "Underline",
  },
  {
    format: "strike",
    iconName: "Strikethrough",
  },
  {
    format: "code",
    iconName: "Code",
  },
];

function FormatHandler({
  editor,
  format,
  iconName,
}: {
  editor: Editor;
  format: string;
  iconName: DynamicIconNameType;
}) {
  const toggleCommands: Record<string, () => ChainedCommands> = {
    bold: () => editor.chain().focus().toggleBold(),
    italic: () => editor.chain().focus().toggleItalic(),
    underline: () => editor.chain().focus().toggleUnderline(),
    strike: () => editor.chain().focus().toggleStrike(),
    code: () => editor.chain().focus().toggleCode(),
  };

  function handlePressChange() {
    const command = toggleCommands[format];

    if (command) {
      command().run();
    }
  }

  return (
    <Toggle
      size="sm"
      pressed={editor.isActive(format)}
      onPressedChange={handlePressChange}
      aria-label={`Toggle ${format} format`}
    >
      <DynamicIcon name={iconName} className="h-4 w-4" />
    </Toggle>
  );
}

interface AlignmentType {
  alignment: string;
  iconName: DynamicIconNameType;
}

const alignments: AlignmentType[] = [
  { alignment: "left", iconName: "AlignLeft" },
  { alignment: "center", iconName: "AlignCenter" },
  { alignment: "right", iconName: "AlignRight" },
  { alignment: "justify", iconName: "AlignJustify" },
];

function AlignmentHandler({
  editor,
  alignment,
  iconName,
}: {
  editor: Editor;
  alignment: string;
  iconName: DynamicIconNameType;
}) {
  return (
    <Toggle
      size="sm"
      pressed={editor.isActive({ textAlign: alignment })}
      onPressedChange={() =>
        editor.chain().focus().setTextAlign(alignment).run()
      }
      aria-label={`Switch ${alignment} alignment`}
    >
      <DynamicIcon name={iconName} className="h-4 w-4" />
    </Toggle>
  );
}

function ImageHandler({ editor }: { editor: Editor }) {
  const [imageSrc, setImageSrc] = useState<string | null>(null);

  function handleFileChange(files: FileList) {
    if (files.length < 1) return;
    const file = files[0];
    if (file) {
      const objectURL = URL.createObjectURL(file);
      setImageSrc(objectURL);
    }
  }

  function handleSubmit(e: FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (editor && imageSrc) {
      editor.chain().focus().setImage({ src: imageSrc }).run();
      setImageSrc(null);
    }
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          aria-label="Insert image"
        >
          <ImageIcon className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent>
        <form
          onSubmit={e => {
            e.stopPropagation();
            handleSubmit(e);
          }}
          className="flex justify-center items-center gap-2"
        >
          <InputFile onValueChange={handleFileChange} />
          <Button
            type="submit"
            variant="ghost"
            size="icon"
            className="shrink-0 h-8 w-8"
            aria-label="Submit"
          >
            <Check className="h-4 w-4" />
          </Button>
        </form>
      </PopoverContent>
    </Popover>
  );
}

function LinkHandler({ editor }: { editor: Editor }) {
  const isLinkActive = editor.isActive("link");

  return isLinkActive ? (
    <Button
      type="button"
      variant="ghost"
      size="icon"
      className="h-8 w-8"
      onClick={() => editor.chain().focus().unsetLink().run()}
      aria-label="Remove link"
    >
      <Unlink className="h-4 w-4" />
    </Button>
  ) : (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          aria-label="Insert link"
        >
          <LinkIcon className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="flex justify-center items-center gap-2">
        <p className="shrink-0">Insert link</p>
        <Input
          autoFocus
          type="text"
          placeholder="https://www.example.com"
          onKeyDown={e => {
            if (e.key === "Enter") {
              editor
                .chain()
                .focus()
                .setLink({
                  href: (e.target as HTMLInputElement).value,
                })
                .run();
            }
          }}
        />
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="shrink-0 h-8 w-8"
          onClick={e => {
            editor
              .chain()
              .focus()
              .setLink({
                href: (e.target as HTMLInputElement).value,
              })
              .run();
          }}
          aria-label="Submit"
        >
          <Check className="h-4 w-4" />
        </Button>
      </PopoverContent>
    </Popover>
  );
}

function ColorHandler({ editor }: { editor: Editor }) {
  const selectedColor = editor.getAttributes("textStyle").color;
  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <Button
      type="button"
      variant="ghost"
      size="icon"
      className="relative overflow-hidden"
      onClick={() => inputRef.current?.click()}
      aria-label="Select text color"
    >
      <Palette style={{ color: selectedColor }} className="size-4" />
      <Input
        ref={inputRef}
        type="color"
        value={selectedColor}
        onChange={e =>
          editor
            .chain()
            .focus()
            .setColor(e.target.value as string)
            .run()
        }
        className="sr-only"
        tabIndex={-1}
      />
    </Button>
  );
}

function HighlightHandler({ editor }: { editor: Editor }) {
  const isHighlighted = editor.isActive("highlight");
  const inputRef = useRef<HTMLInputElement>(null);
  const highlightColor = editor.getAttributes("highlight").color || "#ffff00";

  return (
    <Button
      type="button"
      variant="ghost"
      size="icon"
      className="relative overflow-hidden"
      onClick={() => {
        if (isHighlighted) {
          editor.chain().focus().unsetHighlight().run();
        } else {
          inputRef.current?.click();
        }
      }}
      aria-label="Highlight text"
    >
      <Highlighter
        style={{ color: isHighlighted ? highlightColor : undefined }}
        className="size-4"
      />
      <Input
        ref={inputRef}
        type="color"
        defaultValue={highlightColor}
        onChange={e =>
          editor.chain().focus().setHighlight({ color: e.target.value }).run()
        }
        className="sr-only"
        tabIndex={-1}
      />
    </Button>
  );
}

function SuperscriptHandler({ editor }: { editor: Editor }) {
  return (
    <Toggle
      size="sm"
      pressed={editor.isActive("superscript")}
      onPressedChange={() => editor.chain().focus().toggleSuperscript().run()}
      aria-label="Toggle superscript"
    >
      <Superscript className="size-4" />
    </Toggle>
  );
}

function SubscriptHandler({ editor }: { editor: Editor }) {
  return (
    <Toggle
      size="sm"
      pressed={editor.isActive("subscript")}
      onPressedChange={() => editor.chain().focus().toggleSubscript().run()}
      aria-label="Toggle subscript"
    >
      <Subscript className="size-4" />
    </Toggle>
  );
}

function CodeBlockHandler({ editor }: { editor: Editor }) {
  return (
    <Toggle
      size="sm"
      pressed={editor.isActive("codeBlock")}
      onPressedChange={() => editor.chain().focus().toggleCodeBlock().run()}
      aria-label="Toggle code block"
    >
      <SquareCode className="size-4" />
    </Toggle>
  );
}

function TaskListHandler({ editor }: { editor: Editor }) {
  return (
    <Toggle
      size="sm"
      pressed={editor.isActive("taskList")}
      onPressedChange={() => editor.chain().focus().toggleTaskList().run()}
      aria-label="Toggle task list"
    >
      <ListTodo className="size-4" />
    </Toggle>
  );
}

function BulletListHandler({ editor }: { editor: Editor }) {
  return (
    <Toggle
      size="sm"
      pressed={editor.isActive("bulletList")}
      onPressedChange={() => editor.chain().focus().toggleBulletList().run()}
      aria-label="Toggle bullet list"
    >
      <List className="size-4" />
    </Toggle>
  );
}

function TableHandler({ editor }: { editor: Editor }) {
  const isTableActive = editor.isActive("table");

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          aria-label="Table"
        >
          <Table className="size-4" />
          <span className="sr-only">Table</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-64 p-2">
        <div className="grid gap-2">
          {!isTableActive ? (
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Insert Table</h4>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => {
                    editor
                      .chain()
                      .focus()
                      .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
                      .run();
                  }}
                >
                  <Table className="mr-2 size-4" />
                  <span>3 x 3</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => {
                    editor
                      .chain()
                      .focus()
                      .insertTable({ rows: 5, cols: 5, withHeaderRow: true })
                      .run();
                  }}
                >
                  <Table className="mr-2 size-4" />
                  <span>5 x 5</span>
                </Button>
              </div>
            </div>
          ) : (
            <>
              <h4 className="font-medium text-sm">Rows</h4>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => editor.chain().focus().addRowBefore().run()}
                >
                  <ArrowUp className="mr-2 size-4" />
                  <span>Add Above</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => editor.chain().focus().addRowAfter().run()}
                >
                  <ArrowDown className="mr-2 size-4" />
                  <span>Add Below</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => editor.chain().focus().deleteRow().run()}
                >
                  <Minus className="mr-2 size-4" />
                  <span>Delete Row</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => editor.chain().focus().mergeOrSplit().run()}
                >
                  <Rows className="mr-2 size-4" />
                  <span>Merge/Split</span>
                </Button>
              </div>

              <Separator className="my-2" />

              <h4 className="font-medium text-sm">Columns</h4>
              <div className="grid grid-cols-1 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => editor.chain().focus().addColumnBefore().run()}
                >
                  <ArrowLeft className="mr-2 size-4" />
                  <span>Add Left</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => editor.chain().focus().addColumnAfter().run()}
                >
                  <ArrowRight className="mr-2 size-4" />
                  <span>Add Right</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => editor.chain().focus().deleteColumn().run()}
                >
                  <Minus className="mr-2 size-4" />
                  <span>Delete Column</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() =>
                    editor.chain().focus().toggleHeaderColumn().run()
                  }
                >
                  <Columns className="mr-2 size-4" />
                  <span>Header Column</span>
                </Button>
              </div>

              <Separator className="my-2" />

              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => editor.chain().focus().deleteTable().run()}
              >
                <X className="mr-2 size-4" />
                <span>Delete Table</span>
              </Button>
            </>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}

function YoutubeHandler({ editor }: { editor: Editor }) {
  const [youtubeUrl, setYoutubeUrl] = useState<string>("");

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (youtubeUrl) {
      editor.chain().focus().setYoutubeVideo({ src: youtubeUrl }).run();
      setYoutubeUrl("");
    }
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          aria-label="Insert YouTube video"
        >
          <Youtube className="size-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="flex flex-col gap-2 w-72">
        <form onSubmit={handleSubmit} className="flex flex-col gap-2">
          <p className="text-sm font-medium">Insert YouTube Video</p>
          <Input
            autoFocus
            type="text"
            placeholder="https://www.youtube.com/watch?v=dQw4w9WgXcQ"
            value={youtubeUrl}
            onChange={e => setYoutubeUrl(e.target.value)}
          />
          <Button type="submit" size="sm">
            Insert Video
          </Button>
        </form>
      </PopoverContent>
    </Popover>
  );
}

export function EditorMenuBar({ editor }: { editor: Editor }) {
  return (
    <div
      className="flex flex-wrap items-center gap-1.5 p-1.5"
      aria-label="Editor Menu Bar"
    >
      <SizeHandler editor={editor} />
      <ColorHandler editor={editor} />
      <HighlightHandler editor={editor} />

      <Separator orientation="vertical" className="!h-4" />

      {formats.map(({ format, iconName }) => (
        <FormatHandler
          key={format}
          editor={editor}
          format={format}
          iconName={iconName}
        />
      ))}
      <SuperscriptHandler editor={editor} />
      <SubscriptHandler editor={editor} />

      <Separator orientation="vertical" className="!h-4" />

      {alignments.map(({ alignment, iconName }) => (
        <AlignmentHandler
          key={alignment}
          editor={editor}
          alignment={alignment}
          iconName={iconName}
        />
      ))}

      <Separator orientation="vertical" className="!h-4" />

      <BulletListHandler editor={editor} />
      <TaskListHandler editor={editor} />
      <CodeBlockHandler editor={editor} />
      <TableHandler editor={editor} />

      <Separator orientation="vertical" className="!h-4" />

      <ImageHandler editor={editor} />
      <LinkHandler editor={editor} />
      <YoutubeHandler editor={editor} />
    </div>
  );
}
