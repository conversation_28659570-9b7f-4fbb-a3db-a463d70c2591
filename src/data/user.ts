import type { UserType } from "~/types";

export const userData: UserType = {
  id: "1",
  firstName: "<PERSON>",
  lastName: "<PERSON><PERSON>",
  name: "<PERSON>",
  password: "StrongPass123",
  username: "john.doe",
  role: "Next.js Developer",
  avatar: "/images/avatars/male-01.svg",
  background: "",
  status: "ONLINE",
  phoneNumber: "+***********",
  email: "<EMAIL>",
  state: "California",
  country: "United States",
  address: "123 Main Street, Apt 4B",
  zipCode: "90210",
  language: "English",
  timeZone: "GMT+08:00",
  currency: "USD",
  organization: "Tech Innovations Inc.",
  twoFactorAuth: false,
  loginAlerts: true,
  accountReoveryOption: "email",
  connections: 1212,
  followers: 3300,
};
