/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
import "./src/env.js";

/** @type {import("next").NextConfig} */
const config = {
  reactStrictMode: false,
  async redirects() {
    return [
      // ⚠️ Important:
      // Always list more specific static paths before dynamic ones like "/:lang"
      // to prevent Next.js from incorrectly matching static routes as dynamic parameters.
      // For example, if "/:lang" comes before "/docs", Next.js may treat "docs" as a language.
      {
        source: "/docs",
        destination: "/docs/overview/introduction",
        permanent: true,
      },
      {
        source: "/:lang",
        destination: process.env.NEXT_PUBLIC_HOME_PATHNAME || "/",
        permanent: true,
        has: [
          {
            type: "cookie",
            key: "authjs.session-token",
          },
        ],
      },
      {
        source: "/:lang",
        destination: process.env.NEXT_PUBLIC_HOME_PATHNAME || "/",
        permanent: true,
        has: [
          {
            type: "cookie",
            key: "__Secure-authjs.session-token",
          },
        ],
      },
      {
        source: "/:lang/apps/email",
        destination: "/:lang/apps/email/inbox",
        permanent: true,
      },
    ];
  },
};

export default config;
